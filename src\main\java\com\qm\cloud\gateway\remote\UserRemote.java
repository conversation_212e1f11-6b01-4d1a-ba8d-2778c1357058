package com.qm.cloud.gateway.remote;

import com.qm.cloud.gateway.domain.DTO.DmsLoginDTO;
import com.qm.cloud.gateway.domain.DTO.MobileLoginDTO;
import com.qm.cloud.gateway.domain.DTO.UserDTO;
import com.qm.cloud.gateway.domain.LoginVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 说明：远程调用用户相关
 */
@Repository
@FeignClient(name = "tds-service-sys", fallbackFactory = UserFallback.class, configuration = {KeepErrMsgConfiguration.class})
public interface UserRemote {

    /**
     * 登录
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/user/login")
    JsonResultVo<LoginVO> login(UserDTO user);

    @RequestMapping(value = "/user/loginByPhone")
    JsonResultVo<LoginVO> loginByPhone(MobileLoginDTO mobile);

    @RequestMapping(value = "/user/loginFromDMS")
    JsonResultVo<LoginVO> loginFromDMS(DmsLoginDTO dmsLoginDTO);

    @RequestMapping(value = "/menu/validTransCodeAuth")
    JsonResultVo validTransCodeAuth(@RequestHeader("tenantId") String tenantId,
                                    @RequestHeader("companyId") String companyid,
                                    @RequestHeader("userId") String userId,
                                    @RequestParam(required = true) String transCode,
                                    @RequestParam(required = true) String path);
}
