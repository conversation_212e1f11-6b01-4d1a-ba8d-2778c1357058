package com.qm.cloud.gateway.filter;

import cn.hutool.core.collection.CollUtil;
import com.qm.cloud.gateway.constant.GateExceType;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.jwt.JwtInfo;
import com.qm.cloud.gateway.service.AuthService;
import com.qm.cloud.gateway.service.impl.AuthServiceImpl;
import com.qm.cloud.gateway.util.GateUtils;
import com.qm.tds.api.domain.JsonResultVo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class JwtAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<JwtAuthGatewayFilterFactory.Config> {

    private static final String JWTHEADERNAME_KEY = "jwtHeaderName";
    private static final String ORDER_KEY = "order";
    private static final String STRONG_AUTH = "strongAuth";

    private static final String USER_HEAD_ID = "userId";
    private static final String USER_HEAD_NAME = "userName";

    private AuthServiceImpl gatewayAdminRemote;

    public JwtAuthGatewayFilterFactory(AuthServiceImpl authService) {
        super(Config.class);
        this.gatewayAdminRemote = authService;
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList(JWTHEADERNAME_KEY, ORDER_KEY, STRONG_AUTH);
    }

    @Override
    @SuppressWarnings("squid:S3776")
    public GatewayFilter apply(Config config) {
        AuthService authService = this.gatewayAdminRemote;
        return new OrderedGatewayFilter((exchange, chain) -> {
            String urlPath = exchange.getRequest().getURI().getPath();
            log.info("jwtauth-url:" + urlPath);
            log.info(config.toString());
            String jwt = exchange.getRequest().getHeaders().getFirst(config.jwtHeaderName);

            if (StringUtils.isEmpty(jwt) || jwt.equals("undefined")) {
                if (config.strongAuth) {
                    return GateUtils.errorResponse(exchange, GateExceType.JWTNULL.getType(),
                            GateExceType.JWTNULL.getTypeName());
                } else {
                    // 弱认证情况下，如果请求头没有jwt则忽略。
                    return chain.filter(exchange);
                }
            }
            HttpHeaders uheaders = exchange.getRequest().getHeaders();
            IJwtInfo jwtInfo = new JwtInfo();
            // 通过jwt获取用户信息
            try {
                List<String> useragent = exchange.getRequest().getHeaders().get(GatewayConstants.USERAGENT);
                String userType = "";
                if (CollUtil.isNotEmpty(useragent)) {
                    userType = useragent.toString();
                }
                JsonResultVo<IJwtInfo> resp = authService.getUserInfoByJwt(jwt, userType, urlPath, uheaders.getFirst(GatewayConstants.APPKEY));
                switch (resp.getStatus()) {
                    case "S":
                        jwtInfo = resp.getData();
                        break;
                    default:
                        // 获取用户信息失败
                        // 弱认证情况下，获取不到用户信息也往下游走
                        if (!config.strongAuth) {
                            return chain.filter(exchange);
                        }
                        log.info("---error--"+"getUserByJwt fail: {}", resp.getMsg());
                        return GateUtils.errorResponse(exchange, GateExceType.TOKENFAIL.getType(), resp.getMsg());
                }
            } catch (Exception e) {
                // 网络异常
                log.info("---error--"+"getUserByJwt fail!!! exception:{}, errmsg:{}", e.getClass().getSimpleName(), e.getMessage());
                ServerHttpResponse response = exchange.getResponse();
                String errMsg = "getUserByJwt error! errmsg:" + e.getMessage();
                // 返回错误信息
                return GateUtils.errorResponse(exchange, GateExceType.SYSTEMERROR.getType(), errMsg);
            }

            Map<String, String> paramap = new HashMap<String, String>();
            jwtInfo.getParam().forEach((key, val) -> {
                paramap.put(key, URLEncoder.encode(val));
            });
            ServerHttpRequest request = exchange.getRequest().mutate().header(USER_HEAD_ID, jwtInfo.getId())
                    .header(USER_HEAD_NAME, jwtInfo.getName() == null ? "" : URLEncoder.encode(jwtInfo.getName()))
                    .headers(headers -> headers.setAll(paramap)).build();

            log.info("--添加头信息,userid=" + jwtInfo.getId() + ",username=" + jwtInfo.getName() + ",kickOut="
                    + jwtInfo.getKickout() + ",param=" + paramap);
            return chain.filter(exchange.mutate().request(request).build());
        }, config.getOrder());
    }

    @Data
    public static class Config {
        private String jwtHeaderName;
        private int order;
        // 强验证开关，默认强认证。配置方式：JwtAuth=jwt,3、 JwtAuth=jwt,3,false、JwtAuth=jwt,3,true
        private boolean strongAuth = true;
    }
}
