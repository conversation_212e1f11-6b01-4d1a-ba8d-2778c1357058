package com.qm.cloud.gateway.remote;

import com.qm.cloud.gateway.domain.DTO.DmsLoginDTO;
import com.qm.cloud.gateway.domain.DTO.MobileLoginDTO;
import com.qm.cloud.gateway.domain.DTO.UserDTO;
import com.qm.cloud.gateway.domain.LoginVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;


@Component
public class UserHystrix extends QmRemoteHystrix<UserRemote> implements UserRemote {

    @Autowired
    private I18nUtil i18nUtil;

    private static final String GET_HYSTRIX_EX = "ERR.gatewayservice.remote.getHystrixEx";

    @Override
    public JsonResultVo<LoginVO> login(UserDTO user) {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }

    @Override
    public JsonResultVo<LoginVO> loginByPhone(MobileLoginDTO mobile) {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }

    @Override
    public JsonResultVo<LoginVO> loginFromDMS(DmsLoginDTO dmsLoginDTO) {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }

    @Override
    public JsonResultVo validTransCodeAuth(@RequestHeader("tenantId") String tenantId,
                                           @RequestHeader("companyId") String companyid,
                                           @RequestHeader("userId") String userId,
                                           String transCode,
                                           String path) {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }
}
