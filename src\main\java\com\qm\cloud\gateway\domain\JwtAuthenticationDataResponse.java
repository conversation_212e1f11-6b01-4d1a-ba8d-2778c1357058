package com.qm.cloud.gateway.domain;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
public class JwtAuthenticationDataResponse implements Serializable {

    private static final long serialVersionUID = 3499186841961348924L;
    private final String jwt;
    private final JSONObject rData;

    public JwtAuthenticationDataResponse(String jwt, Object data) {
        this.jwt = jwt;
        rData = JSON.parseObject(data.toString());
    }

    public JwtAuthenticationDataResponse(String jwt, Map<String, String> data) {
        this.jwt = jwt;
        String s = JSONObject.toJSONString(data);
        rData = JSON.parseObject(s);
    }

    public String getJwt() {
        return jwt;
    }

    public JSONObject getrData() {
        return rData;
    }

}
