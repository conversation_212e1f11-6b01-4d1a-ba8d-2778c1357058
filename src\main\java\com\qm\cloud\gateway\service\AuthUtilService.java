package com.qm.cloud.gateway.service;


import com.qm.cloud.gateway.exception.JwtIllegalArgumentException;
import com.qm.cloud.gateway.exception.JwtSignatureException;
import com.qm.cloud.gateway.exception.JwtTokenExpiredException;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.jwt.JwtHelper;
import com.qm.tds.api.exception.QmException;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.SignatureException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AuthUtilService {

    @Value("${jwt.params}")
    private String jwtParams;

    @Value("${jwt.pub-key.path}")
    private String pubKeyPath;

    public IJwtInfo getInfoFromToken(String token) throws QmException {
        try {
            // 动态读取接口返回的各个参数值
            return JwtHelper.getInfoFromToken(token, pubKeyPath);
        } catch (ExpiredJwtException ex) {
            throw new JwtTokenExpiredException("User token expired!");
        } catch (SignatureException ex) {
            throw new JwtSignatureException("User token signature error!");
        } catch (IllegalArgumentException ex) {
            throw new JwtIllegalArgumentException("User token is null or empty!");
        }
    }
}
