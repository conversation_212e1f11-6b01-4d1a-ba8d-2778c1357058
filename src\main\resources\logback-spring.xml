<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>logback</contextName>
    <springProperty scope="context" name="host" source="logs.logstash.host" defaultValue="127.0.0.1"/>
    <springProperty scope="context" name="port" source="logs.logstash.port" defaultValue="4560"/>
    <springProperty scope="context" name="logfilemaxhistory" source="logging.file.max-history" defaultValue="4"/>
    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--按天生成日志-->
    <appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <Prudent>true</Prudent>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>
                logs/EP-GATEWAY/ep_gateway_logs-%d{yyyy-MM-dd}.log
            </FileNamePattern>
            <MaxHistory>${logfilemaxhistory}</MaxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!--            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} [%X{traceId:-},%X{spanId:-},%X{ep-opr-code:-},%X{ep-opr-name:-},%X{ep-opr-id:-},%X{ep-com-id:-},%X{ep-tenant-id:-},%X{ep-logintimestamp:-}] ${PID:- } --- [%t] %logger{36} : %m%n</pattern>
        </layout>
    </appender>

    <!--    <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
            &lt;!&ndash;  这是是logstash服务器地址 端口&ndash;&gt;
            <destination>${uris}</destination>
            &lt;!&ndash;输出的格式，推荐使用这个&ndash;&gt;
            <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            </encoder>
        </appender>-->

    <appender name="stash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${host}:${port}</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder" >
        </encoder>
    </appender>

    <!-- 异步写入日志 -->
    <appender name="api-async" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="stash"/>
        <queueSize>8192</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>

    <logger name="com.qm.cloud.gateway.util.LogStashUtils" additivity="false">
        <appender-ref ref="api-async"/>
    </logger>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="logFile"/>
    </root>

</configuration>
