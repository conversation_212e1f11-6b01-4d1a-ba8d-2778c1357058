#这里填写西班牙语翻译
MSG.gatewayservice.common.reflushSuccess=¡Actualización exitosa!
MSG.gatewayservice.getAllFilter.success=Obtener todos los filtros de la pasarela del sistema con éxito
ERR.gatewayservice.dataSignGatewayFilter.fail=Por favor, póngase en contacto con su administrador para reconfigurar el filtro de la pasarela DataSign, la prioridad de DataSign no debe ser anterior a (menos que) el filtro global HttpRequestFilter
ERR.gatewayservice.dataSignGatewayFilter.signError=La firma de datos es incorrecta
ERR.gatewayservice.common.noauth=Sin permiso de acceso
ERR.gatewayservice.epAuthorizeGatewayFilter.useragent=¡El agente de usuario en el encabezado de la solicitud HTTP no puede estar vacío!
ERR.gatewayservice.httpResponse.toobig=La información del contenido de retorno es demasiado grande
ERR.gatewayservice.exHandler.responseStatus=El recurso solicitado %s no existe, por favor, póngase en contacto con el administrador del sistema para comprobar si el URI es correcto y si las aserciones y rutas correspondientes están configuradas.
ERR.gatewayservice.exHandler.resourceNotFound=Recurso no encontrado
ERR.gatewayservice.exHandler.nullPointer=Anomalía de puntero nulo
ERR.gatewayservice.exHandler.timeout=Tiempo de espera de la solicitud agotado
ERR.gatewayservice.exHandler.hystrixRuntime=¡El archivo adjunto cargado puede exceder el límite de tamaño de archivo adjunto del sistema %s!
ERR.gatewayservice.exHandler.isNullOrEmpty=Anomalía de solicitud:
ERR.gatewayservice.remote.getHystrixEx=¡Error de llamada a la interface!
ERR.gatewayservice.gatewayService.refresh=Por favor, compruebe si el servicio de administración de la pasarela está activo.
ERR.gatewayservice.authService.jwtToken=La información de autentificación es incorrecta, por favor vuelva a iniciar la sesión
ERR.gatewayservice.authService.appkey=La clave de aplicación en el Header no puede estar vacía.
ERR.gatewayservice.authService.overtime=Se ha agotado el tiempo de inicio de sesión, por favor, vuelva a iniciar la sesión
ERR.gatewayservice.common.generateJwtFail=Fallo en la generación de jwt
ERR.gatewayservice.common.sendCodeAgain=Por favor, vuelva a enviar el código de verificación
ERR.gatewayservice.common.codeWrong=Error en el código de autentificación
ERR.gatewayservice.common.paramWrong=Error en los parámetros