package com.qm.cloud.gateway.constant;

public class GatewayConstants {
    /**
     * 这个类不能实例化
     */
    private GatewayConstants() {
    }

    public static final String JWT_CLIENT_DELIMITER = ":>";
    // JWT有效时间，暂为2小时
    public static final Long DEFAULT_JWT_EXPIRE = 60 * 60 * 2L;

    public static final String JWT = "jwt";

    public static final String LOGINNAME = "uLoginName";

    public static final Integer ERRORCODE = 404;

    public static final String IGNOREAUTH = "exportServer";

    public static final String USERAGENT = "useragent";

    public static final String BASE = "base";
    public static final String CUSTOM = "custom";
    public static final String DEFAULT = "pc";

    public static final String LOGINTIMESTAMP = "loginTimestamp";
    public static final String APPKEY = "appkey";
    public static final String SECRETKEY = "secretKey";
}
