#这里填写中文翻译
MSG.gatewayservice.common.reflushSuccess=刷新成功！
MSG.gatewayservice.getAllFilter.success=获取所有系统网关过滤器成功
ERR.gatewayservice.dataSignGatewayFilter.fail=请联系管理人员，重新配置DataSign网关过滤器，DataSign优先级不可优先于（小于）HttpRequestFilter全局过滤器
ERR.gatewayservice.dataSignGatewayFilter.signError=数据签名不正确
ERR.gatewayservice.common.noauth=没有权限访问
ERR.gatewayservice.epAuthorizeGatewayFilter.useragent=HTTP请求header中的useragent不可以为空！
ERR.gatewayservice.httpResponse.toobig=返回内容信息过大
ERR.gatewayservice.exHandler.responseStatus=请求资源%s不存在，请联系系统管理员，检查URI是否正确及其是否配置对应断言和路由！
ERR.gatewayservice.exHandler.resourceNotFound=资源找不到
ERR.gatewayservice.exHandler.nullPointer=空指针异常
ERR.gatewayservice.exHandler.timeout=请求超时
ERR.gatewayservice.exHandler.hystrixRuntime=您上传的附件可能超出了%s系统的附件大小限制！
ERR.gatewayservice.exHandler.isNullOrEmpty=请求异常:
ERR.gatewayservice.remote.getHystrixEx=调用接口异常！
ERR.gatewayservice.gatewayService.refresh=请检查gateway-admin服务是否启动
ERR.gatewayservice.authService.jwtToken=认证信息有误，请重新登录
ERR.gatewayservice.authService.appkey=Header中appkey不能为空！
ERR.gatewayservice.authService.overtime=登录超时，请重新登录
ERR.gatewayservice.common.generateJwtFail=生成jwt失败
ERR.gatewayservice.common.sendCodeAgain=请重新发送验证码
ERR.gatewayservice.common.codeWrong=验证码错误
ERR.gatewayservice.common.paramWrong=参数错误