package com.qm.cloud.gateway.remote;

import com.qm.cloud.gateway.domain.GatewayFilterDO;
import com.qm.cloud.gateway.domain.GatewayRouteVO;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.hystrix.QmRemoteHystrix;
import com.qm.tds.util.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Component
public class GatewayAdminHystrix extends QmRemoteHystrix<GatewayAdminRemote> implements GatewayAdminRemote {

    @Autowired
    private I18nUtil i18nUtil;

    private static final String GET_HYSTRIX_EX = "ERR.gatewayservice.remote.getHystrixEx";

    @Override
    public JsonResultVo<GatewayRouteVO> getGagewayRoutes() {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }

    @Override
    public JsonResultVo<GatewayFilterDO> getGagewayFilters(Map<String, Object> map) {
        JsonResultVo resultObj = new JsonResultVo();
        if (getHystrixEx() == null) {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message);
        } else {
            String message = i18nUtil.getMessage(GET_HYSTRIX_EX);
            resultObj.setMsgErr(message, getHystrixEx());
        }
        resultObj.setCode(500);
        return resultObj;
    }
}
