package com.qm.cloud.gateway.filter;

import cn.hutool.core.util.StrUtil;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.jwt.JwtInfo;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * logout过滤器 用户登出时使用
 *
 * <AUTHOR>
 * @date 2021/4/9 17:22
 */
@Slf4j
@Component
public class LogoutGatewayFilterFactory extends AbstractGatewayFilterFactory<Object> {
    private final RedisUtils redisUtils;

    public LogoutGatewayFilterFactory(RedisUtils redisUtils) {
        this.redisUtils = redisUtils;
    }

    /**
     * @param config 路由配置
     * @return GatewayFilter
     */
    @Override
    public GatewayFilter apply(Object config) {
        return (exchange, chain) -> chain.filter(exchange).then(this.cleanJwt(exchange));
    }

    /**
     * 清除用户登录信息
     *
     * @param exchange 请求/响应交换器
     */
    private Mono<Void> cleanJwt(ServerWebExchange exchange) {
        HttpHeaders headers = exchange.getRequest().getHeaders();
        String useragent = headers.getFirst("useragent");
        String jwt = headers.getFirst("jwt");

        useragent = StrUtil.blankToDefault(useragent, "pc");
        String jwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, "[" + useragent + "]", jwt);
        Object jwtObj = redisUtils.get(jwtKey);
        if (!(jwtObj instanceof JwtInfo) || StrUtil.isBlank(((JwtInfo) jwtObj).getUsername())) {
            log.info("[-LogoutGatewayFilterFactory-].cleanJwt:非法登出,{}中用户缓存信息缺失！", jwtKey);
        } else {
            String loginName = ((JwtInfo) jwtObj).getUsername();
            String userKey = redisUtils.keyBuilder(GatewayConstants.LOGINNAME, "[" + useragent + "]", loginName);
            boolean userKeyDel = redisUtils.del(userKey);
            if (!userKeyDel) {
                log.info("[-LogoutGatewayFilterFactory-].cleanJwt:非法登出,{}相关用户缓存信息删除失败！", userKey);
            }
        }
        boolean jwtKeyDel = redisUtils.del(jwtKey);
        if (!jwtKeyDel) {
            log.info("[-LogoutGatewayFilterFactory-].cleanJwt:非法登出,{}相关用户缓存信息删除失败！", jwtKey);
        }
        return Mono.empty();
    }
}
