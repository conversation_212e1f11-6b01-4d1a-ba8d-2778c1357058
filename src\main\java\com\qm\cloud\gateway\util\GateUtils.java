package com.qm.cloud.gateway.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
public class GateUtils {

    private GateUtils() {
        // Do nothing because of X and Y.
    }

    /**
     * 返回异常信息
     * @param exchange 		ServerWebExchange对象
     * @param resultCode 	异常code
     * @param msg			异常信息
     * @return
     * @throws JsonProcessingException 
     */
    public static Mono<Void>  errorResponse(ServerWebExchange exchange,Integer resultCode,String msg) {
    	try {
            RestResultResponse<String> resp =new RestResultResponse<String>();
            ObjectMapper mapper = new ObjectMapper();
            resp.setResultCode(resultCode);
            resp.setErrMsg(msg);
            String result =  mapper.writeValueAsString(resp);
            DataBuffer bodyDataBuffer = exchange.getResponse().bufferFactory().wrap(result.getBytes());

            exchange.getResponse().getHeaders()
                    .setContentType(MediaType.APPLICATION_JSON_UTF8);

            return exchange.getResponse().writeWith(Flux.just(bodyDataBuffer));
		} catch (Exception e) {
			// TODO: handle exception
			log.info("---error--"+"error",e);
			return Mono.empty();
		}
    }
}
