package com.qm.cloud.gateway.util;

import cn.hutool.core.convert.Convert;
import com.qm.cloud.gateway.config.JwtExpireConfig;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.jwt.JwtHelper;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

/**
 * <AUTHOR>
@Component
public class JwtTokenUtil {
    private static Logger logger = LoggerFactory.getLogger(JwtTokenUtil.class);


    @Value("${jwt.pri-key.path}")
    private String priKeyPath;
    @Value("${jwt.pub-key.path}")
    private String pubKeyPath;
    @Value("${jwt.params}")
    private String jwtParams;

    @Autowired
    private JwtExpireConfig jwtConfig;

    /**
     * 根据loginName中的clientId(设备号),在jwtconfig中获取对应的过期时间,如果获取失败则返回默认值DEFAULT_JWT_EXPIRE
     *
     * @param userType
     * @return 过期时间
     */
    public long getExpire(String userType, String userSource) {
        if (null == userType) {
            return jwtConfig.getJwtExpire(GatewayConstants.DEFAULT, userSource);
        }
        try {
            Long expire = jwtConfig.getJwtExpire(StringUtils.strip(userType, "[]"), userSource);
            if (expire == null) {
                expire = jwtConfig.getJwtExpire(GatewayConstants.DEFAULT, userSource);
            }
            return expire;
        } catch (Exception e) {
            logger.error("getExpire error msg:{}, return default value {}", e.getMessage(), GatewayConstants.DEFAULT_JWT_EXPIRE);
            return GatewayConstants.DEFAULT_JWT_EXPIRE;
        }
    }

    public long getExpire(String loginName) {

        if (null == loginName) {
            return Convert.toLong(jwtConfig.getBase());
        }

        int delimiterIndex;
        String clientId = (delimiterIndex = loginName.lastIndexOf(GatewayConstants.JWT_CLIENT_DELIMITER)) == -1 ? ""
                : loginName.substring(delimiterIndex + GatewayConstants.JWT_CLIENT_DELIMITER.length());
        try {
            Long expire = jwtConfig.getCustomExpire(clientId);
            if (expire == null) {
                expire = Convert.toLong(jwtConfig.getBase());
            }
            return expire;
        } catch (Exception e) {
            logger.error("getExpire error msg:{}, return default value {}", e.getMessage(), GatewayConstants.DEFAULT_JWT_EXPIRE);
            return GatewayConstants.DEFAULT_JWT_EXPIRE;
        }
    }

    public String generateToken(IJwtInfo jwtInfo, String userType, String userSource) throws NoSuchAlgorithmException, IOException, InvalidKeySpecException {
        Long expireLong = getExpire(userType, userSource);
        int expire = expireLong.intValue();
        logger.info("--generateToken,expire=" + expire);
        return JwtHelper.generateToken(jwtInfo, priKeyPath, expire);
    }

    public IJwtInfo getInfoFromToken(String token) {
        return JwtHelper.getInfoFromToken(token, pubKeyPath);
    }

    public String getJwtParams() {
        return jwtParams;
    }
}
