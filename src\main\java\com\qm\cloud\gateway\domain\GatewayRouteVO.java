package com.qm.cloud.gateway.domain;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.qm.tds.api.ser.QmDateSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-28
 */
@Schema(title = "", description = "")
@Data
public class GatewayRouteVO {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String routeName;

    private String predicates;

    private String filters;

    private String uri;

    private Integer routeOrder;

    private String remarks;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date createDate;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = QmDateSerialize.class)
    private Date updateDate;

    private Boolean enabled;

    private List<GatewayPredicateDO> predicateList;

    private List<GatewayFilterDO> filterList;
}