package com.qm.cloud.gateway.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;

/**
 * 
 * @Description
 * <AUTHOR>
 * @Date 2018年8月24日
 */
public class SslUtil {

	private SslUtil() {
		// Do nothing because of X and Y.
	}


	private static Logger logger = LoggerFactory.getLogger(SslUtil.class);


	@SuppressWarnings("squid:S1604")
	public final static HostnameVerifier DO_NOT_VERIFY = new HostnameVerifier() {

	 @Override
	 public boolean verify(String hostname, SSLSession session) {
		 return true;
	 }
	};
	
	public static SSLSocketFactory trustAllHosts(){
		try {
			TrustManager[] trustAllCerts = new TrustManager[] {new MyX509TrustManager()};

			SSLContext sc = SSLContext.getInstance("TLS");
			sc.init(null, trustAllCerts, new java.security.SecureRandom());
			SSLSocketFactory sslSocketFactory = sc.getSocketFactory();
			return sslSocketFactory;
		} catch (Exception e) {
		logger.error("error",e);
			return null;
		}
	}
}
