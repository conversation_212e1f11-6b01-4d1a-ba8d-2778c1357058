package com.qm.cloud.gateway.service;

import com.qm.cloud.gateway.domain.DTO.OnlineUserDTO;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;

import java.util.List;

public interface AuthService {
    JsonResultVo<IJwtInfo> getUserInfoByJwt(String jwtToken, String userType, String urlPath, String appKey) throws QmException;

    /**
     * 根据条件强制用户下线
     *
     * @param users 用户信息
     * @return 强制下线的用户数量
     */
    Long offline(List<OnlineUserDTO> users);
}
