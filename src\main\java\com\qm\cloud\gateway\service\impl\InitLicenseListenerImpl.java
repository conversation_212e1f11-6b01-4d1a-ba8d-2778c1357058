package com.qm.cloud.gateway.service.impl;

import com.qm.cloud.gateway.domain.DTO.GatewayLicense;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class InitLicenseListenerImpl implements InitializingBean {

    private GatewayLicense license = new GatewayLicense();

    @Override
    public void afterPropertiesSet() throws Exception {
        initLicenses();
    }

    public void initLicenses() {
        try {
            List<GatewayLicense> licenses = new ArrayList<>();
            GatewayLicense defaultLicense = new GatewayLicense();
            defaultLicense.setId(1);
            defaultLicense.setAuthorizationCode("3a863974aa4bef6ba71c");
            licenses.add(defaultLicense);
            if (!licenses.isEmpty()) {
                log.info("InitLicense:{}", licenses);
                this.license = licenses.get(0);
            } else {
                log.info("---error--"+"InitLicense fail: licenses is empty");
            }
        } catch (Exception e) {
            log.info("---error--"+"InitLicense exception. {}", e.getMessage());
        }
    }

    public GatewayLicense getLicense() {
        return this.license;
    }

}
