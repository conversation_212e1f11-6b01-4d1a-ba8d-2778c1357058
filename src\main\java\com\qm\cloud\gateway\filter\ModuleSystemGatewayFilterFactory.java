package com.qm.cloud.gateway.filter;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.qm.cloud.gateway.constant.GateExceType;
import com.qm.cloud.gateway.util.GateUtils;
import com.qm.cloud.gateway.util.HttpToolkitUtils;
import com.qm.cloud.gateway.util.RestResultResponse;
import feign.Client;
import feign.RequestLine;
import feign.hystrix.SetterFactory;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 子系统功能二次开发
 * 目标：不依赖admin系统
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModuleSystemGatewayFilterFactory
        extends AbstractGatewayFilterFactory<ModuleSystemGatewayFilterFactory.Config> {

    private static String[] patterns = {"Cookie", "Postman-Token", "Host", "Connection", "Content-Length", "Upgrade-Insecure-Requests", "Cache-Control", "Origin", "User-Agent", "Content-Type", "Accept", "Accept-Encoding", "Accept-Language", "X-Real-Ip", "X-Forwarded-For"};

    private static String DEFAULT_KICK = "default";

    private static String PREFIX_MODULE = "auth:mod:";

    private static Pattern p = Pattern.compile("^lb:.*");

    @Autowired
    private RedisTemplate redisTemplate;

    private Client client;

    public ModuleSystemGatewayFilterFactory(RedisTemplate redisTemplate, Client client) {
        super(Config.class);
        this.redisTemplate = redisTemplate;
        this.client = client;
    }

    public ModuleSystemGatewayFilterFactory() {
        super(Config.class);
    }

    SetterFactory commandKeyIsRequestLine = (target, method) -> {
        URL url = null;
        try {
            url = new URL(target.name());
        } catch (MalformedURLException e) {
            log.info("---error--"+"error", e);
        }

        String groupKey = url.getHost();
        String commandKey = method.getAnnotation(RequestLine.class).value();
        return HystrixCommand.Setter
                .withGroupKey(HystrixCommandGroupKey.Factory.asKey(groupKey))
                .andCommandKey(HystrixCommandKey.Factory.asKey(commandKey))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter().withRequestCacheEnabled(false))
                .andCommandPropertiesDefaults(HystrixCommandProperties.Setter().withExecutionTimeoutInMilliseconds(5 * 1000));
    };

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("tokenHeader", "authUrl", "strongAuth", "enableCache", "cacheTime", "enableKickOut", "enableClientkickOut", "primary", "clientHeader", "order");
    }

    @SuppressWarnings("all")
    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            try {
                log.info("url:" + exchange.getRequest().getURI().toString());
                if (!checkConfig(config)) {
                    return GateUtils.errorResponse(exchange, GateExceType.SYSTEMCODEERROR.getType(), GateExceType.SYSTEMCODEERROR.getTypeName());
                }
                String token = getValueByRequest(exchange, config.tokenHeader);
                if (StrUtil.isBlank(token)) {
                    log.info("请求中无token");
                    // 如果是弱认证，直接转发
                    if (!config.strongAuth) {
                        return chain.filter(exchange);
                    }
                    return GateUtils.errorResponse(exchange, GateExceType.TOKENULL.getType(), GateExceType.TOKENULL.getTypeName());
                }
                log.info("token-->{}", token);
                //请求url参数
                String params = exchange.getRequest().getURI().getRawQuery();
                //header信息
                Map<String, String> headerMap = exchange.getRequest().getHeaders().toSingleValueMap();
                //去除无价值头信息
                Stream.of(patterns).forEach(pattern -> headerMap.remove(pattern));
                Map<String, Object> userInfo = new HashMap<>();
                if (config.enableCache) {
                    //依赖缓存redis
                    // 判断当前的设备
                    String client = DEFAULT_KICK;
                    if (config.enableKickOut && config.enableClientkickOut) {
                        client = exchange.getRequest().getHeaders().getFirst(config.clientHeader);
                        if (StrUtil.isBlank(client)) {
                            //设备信息不存在，结束请求
                            return GateUtils.errorResponse(exchange, GateExceType.DEVICENULL.getType(), GateExceType.DEVICENULL.getTypeName());
                        }
                    }
                    log.info("device-->{}", client);
                    //toekn服务区分
                    token = PREFIX_MODULE + token;
                    // 查询缓存数据
                    userInfo = (Map<String, Object>) redisTemplate.opsForValue().get(token);
                    if (userInfo == null) {
                        //redis没有用户信息
                        RestResultResponse resp = getUserInfo(config, params, headerMap);
                        if (resp.getData() != null) {
                            // 获取了用户数据
                            userInfo = (Map<String, Object>) resp.getData();
                            // 缓存用户信息
                            redisTemplate.opsForValue().set(token, userInfo, config.cacheTime, TimeUnit.SECONDS);
                            //检查是否执行互踢
                            if (config.enableKickOut) {
                                //1.获取primary
                                String userId = PREFIX_MODULE + String.valueOf(userInfo.get(config.primary));
                                //2.查询旧的token
                                String oldToken = (String) redisTemplate.opsForHash().get(userId, client);
                                //3.如果token不一致,删除旧的token,并在hash中插入新的token
                                if (oldToken != null && !oldToken.equals(token)) {
                                    // 踢掉旧的
                                    redisTemplate.delete(oldToken);
                                }
                                redisTemplate.opsForHash().put(userId, client, token);
                                //设置过期时间
                                redisTemplate.expire(userId, config.cacheTime, TimeUnit.SECONDS);
                            }
                        } else {
                            //"token is error:check userAuth"
                            // 如果是弱认证，直接转发
                            if (!config.strongAuth) {
                                return chain.filter(exchange);
                            }
                            return GateUtils.errorResponse(exchange, GateExceType.TOKENFAIL.getType(), GateExceType.TOKENFAIL.getTypeName());
                        }
                    } else {
                        //redis有用户信息,1.延长过期时间
                        // 延长token缓存时间
                        redisTemplate.expire(token, config.cacheTime, TimeUnit.SECONDS);
                        if (config.enableKickOut) {
                            //开启互踢，需要延长primary过期时间
                            String userId = PREFIX_MODULE + String.valueOf(userInfo.get(config.primary));
                            redisTemplate.expire(userId, config.cacheTime, TimeUnit.SECONDS);
                        }

                    }
                } else {
                    //不依赖缓存
                    RestResultResponse resp = getUserInfo(config, params, headerMap);
                    if (resp.getData() != null) {
                        // 获取了用户信息
                        userInfo = (Map<String, Object>) resp.getData();
                    } else {
                        log.info("token is error:check userAuth");
                        // 如果是弱认证，直接转发
                        if (!config.strongAuth) {
                            return chain.filter(exchange);
                        }
                        return GateUtils.errorResponse(exchange, GateExceType.TOKENFAIL.getType(), GateExceType.TOKENFAIL.getTypeName());
                    }
                }
                // 对象转换
                Map<String, String> headerInfo = userInfo.entrySet().stream().collect(Collectors.toMap(Entry::getKey, this::charEncode));
                // 存入webexchage
                ServerHttpRequest request = exchange.getRequest().mutate().headers(header -> header.setAll(headerInfo))
                        .build();
                return chain.filter(exchange.mutate().request(request).build());
            } catch (Exception e) {
                log.info("---error--"+"error", e);
                // 如果是弱认证，直接转发
                if (!config.strongAuth) {
                    return chain.filter(exchange);
                }
                return GateUtils.errorResponse(exchange, GateExceType.SYSTEMERROR.getType(), GateExceType.SYSTEMERROR.getTypeName());
            }
        }, config.getOrder());
    }

    @Data
    public static class Config {
        //token的header信息
        private String tokenHeader;

        //认证的url
        private String authUrl;

        // 默认强认证
        private boolean strongAuth = true;

        // 是否开启缓存
        private boolean enableCache = true;

        //缓存时间
        private long cacheTime = 3600l;

        // 是否开启互踢
        private boolean enableKickOut = true;

        // 是否开启设备间互踢
        private boolean enableClientkickOut = false;

        // 用户信息主键属性
        private String primary;

        //设备的header信息
        private String clientHeader;


        private int order = 5;

        //检查url是否是服务 true:是服务  false：不是
        private boolean authUrlFlag;

        private String realUrl;

        //解析authUrl,确定是服务调用还是url调用
        public void setAuthUrl(String authUrl) {
            this.authUrl = authUrl;
            Matcher m = p.matcher(authUrl);
            if (m.matches()) {
                //是服务
                this.authUrlFlag = true;
                this.realUrl = authUrl.substring(3).trim();
            } else {
                this.authUrlFlag = false;
                this.realUrl = authUrl.trim();
            }

        }

    }

    /**
     * 检查参数维护是否有问题
     *
     * @param config
     * @return
     */
    public boolean checkConfig(Config config) {
        if (StrUtil.isBlank(config.tokenHeader) || StrUtil.isBlank(config.authUrl)) {
            return false;
        }
        if (config.enableKickOut && StrUtil.isBlank(config.primary)) {
            //开启了互踢，则primary不能为null
            return false;
        }
        if (config.enableKickOut && config.enableClientkickOut && StrUtil.isBlank(config.clientHeader)) {
            return false;
        }
        return true;
    }

    /**
     * 获取用户信息
     *
     * @param config
     * @param params
     * @param headers
     * @return
     */
    public RestResultResponse getUserInfo(Config config, String params, Map<String, String> headers) {
        String url = new StringBuffer(config.realUrl).append("?").append(params == null ? "" : params).toString();
        RestResultResponse<Map<String, Object>> restResult = new RestResultResponse<>();
        if (config.authUrlFlag) {
            //服务调用
            Map<String, Collection<String>> header = headers.entrySet().stream().collect(Collectors.toMap(Entry::getKey, e -> {
                List<String> values = new ArrayList<>();
                values.add(e.getValue());
                return values;
            }));
        } else {
            //url调用
            restResult = HttpToolkitUtils.sendDataAndGet(url, null, headers, RestResultResponse.class);
        }
        log.info("result:{}", JSON.toJSONString(restResult));
        if (restResult.getResultCode() == null || !restResult.getResultCode().equals(200)) {
            restResult.data(null);
        }
        return restResult;
    }


    /**
     * api调用时对中文编码
     *
     * @param entry
     * @return
     */
    public String charEncode(Entry entry) {
        try {
            return URLEncoder.encode(String.valueOf(entry.getValue()), "UTF-8");
        } catch (Exception e) {
            return String.valueOf(entry.getValue());
        }

    }

    /**
     * 根据key值，从请求信息中的获取对应value
     *
     * @param exchange
     * @param key
     * @return
     */
    public String getValueByRequest(ServerWebExchange exchange, String key) {
        String token = exchange.getRequest().getHeaders().getFirst(key);
        if (token == null || "".equals(token) || token.equals("undefined")) {
            log.info("header中无token,从参数中找");
            token = exchange.getRequest().getQueryParams().getFirst(key);
        }
        return token;
    }

}
