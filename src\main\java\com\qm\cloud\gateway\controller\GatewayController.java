package com.qm.cloud.gateway.controller;

import com.qm.cloud.gateway.domain.DTO.OnlineUserDTO;
import com.qm.cloud.gateway.service.AuthService;
import com.qm.cloud.gateway.service.GatewayService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import jakarta.annotation.Resource;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/gateway")
@Slf4j
public class GatewayController {

    @Autowired
    private GatewayService gatewayService;
    @Autowired
    private DiscoveryClient discoveryClient;
    @Autowired
    private Environment environment;
    @Autowired
    private I18nUtil i18nUtil;
    @Autowired
    private AuthService authService;

    @Resource
    private RouteDefinitionLocator routeDefinitionLocator;
    @Value("${spring.application.name}")
    private String serverName;

    /**
     * 获取网关所有的路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/routes")
    @Operation(summary = "获取网关所有的路由信息", description = "获取网关所有的路由信息[author:10027705]")
    public Flux<RouteDefinition> getRouteDefinitions() {
        return routeDefinitionLocator.getRouteDefinitions();
    }

    @GetMapping("/refresh")
    @Operation(summary = "刷新路由", description = "刷新路由[author:10027705]")
    public JsonResultVo refresh() {
        log.info("刷新加载路由....");
        // 获取所有网关服务实例
        List<ServiceInstance> instances = discoveryClient.getInstances(serverName);
        instances.forEach(serviceInstance -> {
            log.info("服务id：{}，元数据：{}，调用IP:{}，端口:{}，刷新路由"
                    , serviceInstance.getServiceId(), serviceInstance.getMetadata()
                    , serviceInstance.getHost(), serviceInstance.getPort());
            // 刷新网关实例
            refreshRemoteGateway(serviceInstance.getHost(), serviceInstance.getPort());
        });
        JsonResultVo result = new JsonResultVo();
        result.setCode(HttpStatus.OK.value());
        String message = i18nUtil.getMessage("MSG.gatewayservice.common.reflushSuccess");
        result.setMsg(message);
        return result;
    }

    @GetMapping("/remoteRefresh")
    @Operation(summary = "远程调用刷新路由", description = "远程调用刷新路由[author:10027705]")
    public Mono<JsonResultVo> remoteRefresh() throws QmException {
        log.info("远程调用刷新加载路由....");
        return Mono.fromCallable(() -> {
            gatewayService.refresh();
            JsonResultVo result = new JsonResultVo();
            result.setCode(HttpStatus.OK.value());
            String message = i18nUtil.getMessage("MSG.gatewayservice.common.reflushSuccess");
            result.setMsg(message);
            return result;
        }).subscribeOn(Schedulers.boundedElastic());
//        gatewayService.refresh();
//        JsonResultVo result = new JsonResultVo();
//        result.setCode(HttpStatus.OK.value());
//        String message = i18nUtil.getMessage("MSG.gatewayservice.common.reflushSuccess");
//        result.setMsg(message);
//        return result;
    }

    @Operation(summary = "根据条件强制用户下线", description = "根据条件强制用户下线[author:10027705]")
    @PostMapping("/offline")
    public JsonResultVo<Long> offline(@RequestBody List<OnlineUserDTO> users) {
        Long count = authService.offline(users);
        JsonResultVo<Long> ret = new JsonResultVo<>();
        ret.setData(count);
        return ret;
    }

    /**
     * 刷新其他网关实例
     *
     * @param host 网关ip
     * @param port 网关端口
     */
    private void refreshRemoteGateway(String host, int port) {
        // 获取http前缀
        String prefix = environment.getProperty("http.prefix");
        prefix = BootAppUtil.isNullOrEmpty(prefix) ? "http://" : prefix;
        String url = prefix + host + ":" + port + "/gateway/remoteRefresh";
        OkHttpClient client = new OkHttpClient();
        //创建一个Request
        Request request = new Request.Builder()
                .get()
                .url(url)
                .build();
        //通过client发起请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                log.info("---error--"+"地址：{}:{}刷新失败！", host, port, e);
            }

            @Override
            public void onResponse(Call call, Response response) {
                if (response.isSuccessful()) {
                    log.debug("地址：{}:{}刷新成功！", host, port);
                }
            }
        });
    }


}
