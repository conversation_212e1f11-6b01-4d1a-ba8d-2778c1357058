package com.qm.cloud.gateway.filter;

import com.qm.cloud.gateway.constant.GateExceType;
import com.qm.cloud.gateway.service.impl.InitLicenseListenerImpl;
import com.qm.cloud.gateway.util.GateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class MixAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<MixAuthGatewayFilterFactory.Config> {

    private static final String JWTHEADERNAME_KEY = "jwtHeaderName";
    private static final String ORDER_KEY = "order";
    private static final String STRONG_AUTH = "strongAuth";
    private static final String MUTATE_HEADER_SUFFIX = "ORG";

    private JwtAuthGatewayFilterFactory jwtAuthGatewayFilterFactory;
    private InitLicenseListenerImpl licenseListener;

    public MixAuthGatewayFilterFactory(JwtAuthGatewayFilterFactory jwtAuthGatewayff,
                                       InitLicenseListenerImpl licenseListener) {
        super(Config.class);
        this.jwtAuthGatewayFilterFactory = jwtAuthGatewayff;
        this.licenseListener = licenseListener;
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList(JWTHEADERNAME_KEY, ORDER_KEY, STRONG_AUTH);
    }

    @Override
    public GatewayFilter apply(Config config) {
        JwtAuthGatewayFilterFactory jwtAuthGatewayff = this.jwtAuthGatewayFilterFactory;
        InitLicenseListenerImpl license = this.licenseListener;
        return new OrderedGatewayFilter((exchange, chain) -> {
            String jwt = exchange.getRequest().getHeaders().getFirst(config.jwtHeaderName);
            if (StringUtils.isEmpty(jwt)) {
                // 如果是弱认证，直接转发
                if (!config.strongAuth) {
                    return chain.filter(exchange);
                }
                log.info("header中无token,调用失败");
                return GateUtils.errorResponse(exchange, GateExceType.TOKENULL.getType(),
                        GateExceType.TOKENULL.getTypeName());
            }
            String authorizationCode = license.getLicense().getAuthorizationCode();
            log.info("jwt:{}", jwt);
            log.info("authorizationCode:{}", authorizationCode);
            // 本机颁发的jwt、视为主应用
            JwtAuthGatewayFilterFactory.Config jwtconfig = jwtAuthGatewayff
                    .newConfig();
            jwtconfig.setJwtHeaderName(config.jwtHeaderName + MUTATE_HEADER_SUFFIX);
            jwtconfig.setOrder(config.order);
            jwtconfig.setStrongAuth(config.strongAuth);
            GatewayFilter filter = jwtAuthGatewayff.apply(jwtconfig);
            // 还原jwt，license不参与用户 验证
            ServerHttpRequest request = exchange.getRequest().mutate()
                    .header(config.jwtHeaderName + MUTATE_HEADER_SUFFIX, jwt)
                    .build();
            return filter.filter(exchange.mutate().request(request).build(), chain);
        }, config.getOrder());
    }

    @Data
    public static class Config {
        private String jwtHeaderName;
        private int order;
        // 强验证开关，默认强认证。配置方式：MixAuth=jwt,3、 MixAuth=jwt,3,false、MixAuth=jwt,3,true
        private boolean strongAuth = true;

        boolean getStrongAuth() {
            return strongAuth;
        }
    }
}
