package com.qm.cloud.gateway.domain;

import java.io.Serializable;

public class ResultBean implements Serializable {

    public ResultBean() {
        super();
    }

    private static final long serialVersionUID = -1092646848721671618L;

    /**
     * API 调用成功
     */
    public static final Integer SUCCESS = 200;

    /**
     * 验证失败
     */
    public static final Integer VALID_FAILD = 400;

    /**
     * 未知错误
     */
    public static final Integer ERROR_UNKNOWN = 900;

    public static final Integer ERROR_DB = 901;

    private Integer code;

    private String msg;

    private long elapsedMilliseconds;

    public ResultBean(Integer resultCode, String errMsg) {
        code = resultCode;
        msg = errMsg;
    }


    public long getElapsedMilliseconds() {
        return elapsedMilliseconds;
    }

    public void setElapsedMilliseconds(long elapsedMilliseconds) {
        this.elapsedMilliseconds = elapsedMilliseconds;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
