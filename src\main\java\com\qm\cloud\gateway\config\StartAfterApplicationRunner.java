package com.qm.cloud.gateway.config;

import com.qm.cloud.gateway.service.GatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2021/1/2$ 11:00$
 **/
@Slf4j
@Component
public class StartAfterApplicationRunner implements CommandLineRunner {

    @Autowired
    private GatewayService gatewayService;

    @Override
    public void run(String... args) throws Exception {
        log.info("初始化加载路由....");
        gatewayService.refresh();
    }
}
