package com.qm.cloud.gateway.util;

import com.alibaba.fastjson.JSONObject;
import com.qm.tds.base.domain.LoginKeyDO;
import io.netty.util.ReferenceCountUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: logstash 日志收集
 */
@Slf4j
public class LogStashUtils {
    public static final String ACCESS_TIME = "accessTime";
    public static final String START_TIME = "startTime";
    public static final String HTTP_REQUEST = "request";
    public static final String CACHE_REQUEST_BODY_OBJECT_KEY = "cachedRequestBodyObject";

    /* log日志的key */
    public static final String LOG_KEY_SERVICE_NAME = "SERVICE_NAME";
    public static final String LOG_KEY_OUT_PARAMETERS = "OUT_PARAMETERS";
    public static final String LOG_KEY_IP = "IP";
    public static final String LOG_VALUE_NULL = "null";
    public static final String COM_DISCOVERY_CLIENT = "CompositeDiscoveryClient_";

    /**
     * 这个类不能实例化
     */
    private LogStashUtils() {
    }

    @SuppressWarnings("squid:S3776")
    public static void putGateWayLogtoLogStash(String logName, ServerHttpRequest originalRequest, Object responseData, ServerWebExchange exchange) {
        Route route = exchange.getAttribute(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        JSONObject json = new JSONObject();
        //请求服务名称
        if (route == null) {
            json.put(LOG_KEY_SERVICE_NAME, LOG_VALUE_NULL);
        } else if (route.getId().contains(COM_DISCOVERY_CLIENT)) {
            json.put(LOG_KEY_SERVICE_NAME, route.getId().replaceAll(COM_DISCOVERY_CLIENT, ""));
        } else {
            json.put(LOG_KEY_SERVICE_NAME, route.getId());
        }
        //请求uri
        json.put("URI", originalRequest.getURI().getPath());
        //请求类型
        json.put("HTTP_METHOD", originalRequest.getMethod());
        try {
            //访问者ip
            json.put(LOG_KEY_IP, originalRequest.getRemoteAddress().getAddress().getHostAddress());
        } catch (NullPointerException ex) {
            json.put(LOG_KEY_IP, LOG_VALUE_NULL);
        }
        //访问时间
        json.put("ACCESS_TIME", exchange.getAttribute(ACCESS_TIME));
        //入参
        json.put("ENTRY_PARAMETERS", exchange.getAttribute(CACHE_REQUEST_BODY_OBJECT_KEY));
        //自定义名称
        json.put("LOG_SERVER_NAME", logName);
        //request header中信息
        json.put("HEADER_INFO", getLoginKey(originalRequest));
        //请求 Content-Type
        json.put("REQUEST_CONTENT_TYPE", originalRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE));
        //出参
        if (responseData == null) {
            JSONObject resJson = new JSONObject();
            resJson.put("msg", "不记录[" + exchange.getAttribute(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR) + "]日志!");
            json.put(LOG_KEY_OUT_PARAMETERS, resJson.toJSONString());
        } else {
            if (responseData instanceof JSONObject || (responseData.toString().startsWith("{") && responseData.toString().endsWith("}")))
                json.put(LOG_KEY_OUT_PARAMETERS, "" + responseData);
            else
                json.put(LOG_KEY_OUT_PARAMETERS, "{" + responseData + "}");
        }
        //响应 Content-Type
        json.put("RESPONSE_CONTENT_TYPE", exchange.getAttribute(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR));
        //消耗时间
        json.put("TIME_CONSUMING", System.currentTimeMillis() - Long.parseLong(exchange.getAttribute(START_TIME).toString()));
        //状态
        HttpStatusCode status = exchange.getResponse().getStatusCode();
        if (null != status) {
            json.put("STATUS", status.value());
            if (200 == status.value()) {
                log.info(json.toJSONString());
            } else {
                log.info("---error--"+json.toJSONString());
            }
        } else {
            log.info("---error--"+json.toJSONString());
        }
    }

    public static void putGateWayException(String logName, Map<String, Object> responseData, Map<String, Object> attributes) {
        Route route = (Route) attributes.get(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
        ServerHttpRequest originalRequest = (ServerHttpRequest) attributes.get(HTTP_REQUEST);
        JSONObject json = new JSONObject();
        //请求服务名称
        if (route == null) {
            json.put(LOG_KEY_SERVICE_NAME, LOG_VALUE_NULL);
        } else if (route.getId().contains(COM_DISCOVERY_CLIENT)) {
            json.put(LOG_KEY_SERVICE_NAME, route.getId().replaceAll(COM_DISCOVERY_CLIENT, ""));
        } else {
            json.put(LOG_KEY_SERVICE_NAME, route.getId());
        }
        //请求uri
        if (null != originalRequest) {
            json.put("URI", originalRequest.getURI().getPath());
            //请求类型
            json.put("HTTP_METHOD", originalRequest.getMethod());
            try {
                //访问者ip
                json.put(LOG_KEY_IP, originalRequest.getRemoteAddress().getAddress().getHostAddress());
            } catch (NullPointerException ex) {
                json.put(LOG_KEY_IP, LOG_VALUE_NULL);
            }
            //request header中信息
            json.put("HEADER_INFO", getLoginKey(originalRequest));
            //请求 Content-Type
            json.put("REQUEST_CONTENT_TYPE", originalRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE));
        }
        //访问时间
        json.put("ACCESS_TIME", attributes.get(ACCESS_TIME));
        //入参
        json.put("ENTRY_PARAMETERS", attributes.get(CACHE_REQUEST_BODY_OBJECT_KEY));
        //自定义名称
        json.put("LOG_SERVER_NAME", logName);

        //出参
        JSONObject resJson = new JSONObject();
        resJson.put("msg", responseData.get("msg"));

        json.put(LOG_KEY_OUT_PARAMETERS, "" + resJson);
        //响应 Content-Type
        json.put("RESPONSE_CONTENT_TYPE", attributes.get(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR));
        //状态
        json.put("STATUS", responseData.get("code"));
        //消耗时间
        if (null != attributes.get(START_TIME))
            json.put("TIME_CONSUMING", System.currentTimeMillis() - Long.parseLong(attributes.get(START_TIME).toString()));
        if (200 == Integer.parseInt(responseData.get("code").toString())) {
            log.info(json.toJSONString());
        } else {
            log.info("---error--"+json.toJSONString());
        }
    }

    public static LoginKeyDO getLoginKey(ServerHttpRequest originalRequest) {
        LoginKeyDO loginKey = new LoginKeyDO();
        HttpHeaders headers = originalRequest.getHeaders();
        String companyId = headers.getFirst("companyId");
        String userId = headers.getFirst("userId");
        String userName = headers.getFirst("userName");
        String loginTimestamp = headers.getFirst("loginTimestamp");
        String lang = headers.getFirst("lang");
        String personCode = headers.getFirst("personCode");
        String custGroupId = headers.getFirst("custGroupId");
        String appId = headers.getFirst("appId");
        String tenantId = headers.getFirst("tenantId");
        if (userName != null) {
            try {
                userName = URLDecoder.decode(userName, "UTF-8");
            } catch (UnsupportedEncodingException var13) {
                log.info("---error--"+var13.getMessage());
            }
        }
        loginKey.setCompanyId(companyId);
        loginKey.setOperatorId(userId);
        loginKey.setOperatorName(userName);
        loginKey.setLoginTimestamp(loginTimestamp);
        loginKey.setLanguageCode(StringUtils.isBlank(lang) ? "zh" : lang);
        loginKey.setCustGroupid(custGroupId);
        loginKey.setPersonCode(personCode);
        loginKey.setAppId(appId);
        loginKey.setTenantId(tenantId);
        return loginKey;
    }

}
