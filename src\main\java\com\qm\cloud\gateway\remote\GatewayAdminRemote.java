package com.qm.cloud.gateway.remote;

import com.qm.cloud.gateway.domain.GatewayFilterDO;
import com.qm.cloud.gateway.domain.GatewayRouteVO;
import com.qm.tds.api.domain.JsonResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Repository;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;

@Repository
@FeignClient(name = "${feignUrl.gateway-admin}", fallbackFactory = GatewayAdminFallback.class, configuration = {KeepErrMsgConfiguration.class})
public interface GatewayAdminRemote {

    @RequestMapping(value = "/gatewayRoute/all", method = RequestMethod.POST)
    JsonResultVo<GatewayRouteVO> getGagewayRoutes();

    @RequestMapping(value = "/gatewayFilter/tableByParam", method = RequestMethod.POST)
    JsonResultVo<GatewayFilterDO> getGagewayFilters(@RequestBody Map<String, Object> map);
}
