package com.qm.cloud.gateway.constant;

/**
 * 网关异常状态
 * 
 * <AUTHOR>
 *
 */
public enum GateExceType {

	/**
	 * 未知异常
	 */
	SYSTEMERROR(900, "unknown error"),
	/**
	 * 系统维护数据异常
	 */
	SYSTEMCODEERROR(901, "System variable is error"),
	/**
	 * header头中无参数，或者无appid对应的密钥对
	 */
	CLIENTVERIFYFAIL(40101, "MD5 Secret is Error"),
	/**
	 * MD5/rsa签名验证失败
	 */
	SIGNFAIL(40103, "Sign is Error"),
	/**
	 * 子系统token为空
	 */
	TOKENULL(40104, "Token is null"),
	/**
	 * 子系统token验证失败
	 */
	TOKENFAIL(40105, "Token is Error"),

	DEVICENULL(40301, "device is null"),

	/**
	 * RSA的header头信息不全，或者无appid对应的密钥对
	 */
	CLIENTRSAFAIL(40106, "RSA Secret is Error"),
	/**
	 * 权限验证失败
	 */
	PERMISSIONFAIL(40107, "Permission check fail"),

	/**
	 * JWT为空
	 */
	JWTNULL(40108, "JWT is null"),
	/**
	 * dnyAuth用户 id为空
	 */
	USERIDNULL(40109, "userId is null"),
	/**
	 * JWT验证失败
	 */
	JWTFAIL(40110, "JWT is error"),
	/**
	 * 权限验证-用户不存在
	 */
	USERISNULL(40111, "Permission user is null"),
	/**
	 * 微信账号未绑定
	 */
	WX_UNBOUND(40304, "Wechat Account unbound"),
	/**
	 * 服务降级
	 */
	FALLBACK(50101,"Service fallback");
	private Integer type;
	private String typeName;

	private GateExceType(Integer type, String typeName) {
		this.type = type;
		this.typeName = typeName;
	}

	/**
	 * 根据类型获取类型名
	 * 
	 * @param type
	 * @return
	 */
	public static String getTypeName(Integer type) {
		for (GateExceType e : GateExceType.values()) {
			if (e.getType().equals(type)) {
				return e.getTypeName();
			}
		}
		return null;
	}

	public Integer getType() {
		return type;
	}

	private void setType(Integer type) {
		this.type = type;
	}

	public String getTypeName() {
		return typeName;
	}

	private void setTypeName(String typeName) {
		this.typeName = typeName;
	}
}
