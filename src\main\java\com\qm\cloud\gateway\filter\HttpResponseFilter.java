package com.qm.cloud.gateway.filter;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.qm.cloud.gateway.config.properties.LogProperties;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.util.LogStashUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;

/**
 * @description: 获取响应信息
 * @author: Cyl
 * @time: 2020/9/3 14:28
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "logs", value = {"enabled"}, havingValue = "true")
public class HttpResponseFilter implements GlobalFilter, Ordered {

    @Autowired
    private LogProperties logProperties;
    @Autowired
    private I18nUtil i18nUtil;

    @Value("${logs.responseOutLimit:524288}")
    private long responseOutLimit;

    private static final Map<HttpStatus.Series, String> SERIES_VIEWS;

    static {
        Map<HttpStatus.Series, String> views = new EnumMap<>(HttpStatus.Series.class);
        views.put(HttpStatus.Series.CLIENT_ERROR, "4xx");
        views.put(HttpStatus.Series.SERVER_ERROR, "5xx");
        SERIES_VIEWS = Collections.unmodifiableMap(views);
    }

    @SuppressWarnings("squid:S3776")
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpResponse originalResponse = exchange.getResponse();
        ServerHttpRequest originalRequest = exchange.getRequest();
        DataBufferFactory bufferFactory = originalResponse.bufferFactory();
        String uri = originalRequest.getURI().getPath();
        String logName = logProperties.getLogName();
        String[] values = logProperties.getIgnoreLogUri().split(";");
        List<String> list = Arrays.asList(values);
        // 导出放行，需要判断header里的属性
        String ignoreAuth = originalRequest.getHeaders().getFirst(GatewayConstants.IGNOREAUTH);
        ServerHttpResponseDecorator response = new ServerHttpResponseDecorator(originalResponse) {
            @Override
            public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                String contentType = getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);
                long contentLength = getHeaders().getFirst(HttpHeaders.CONTENT_LENGTH) != null ? Long.parseLong(getHeaders().getFirst(HttpHeaders.CONTENT_LENGTH)) : 0;
                HttpStatus status = (HttpStatus) getStatusCode();
                if (!BootAppUtil.isNullOrEmpty(ignoreAuth) && "true".equals(ignoreAuth)) {
                    LogStashUtils.putGateWayLogtoLogStash(logName, originalRequest, null, exchange);
                } else if (null != contentType && contentType.toLowerCase().contains(MediaType.APPLICATION_JSON_VALUE) && contentLength < responseOutLimit) {
                    if (body instanceof Flux) {
                        return super.writeWith(DataBufferUtils.join((Publisher<DataBuffer>) body).map(dataBuffers -> {
                            if (dataBuffers.readableByteCount() < responseOutLimit) {
                                String responseData = "";
                                try {
                                    byte[] content = new byte[dataBuffers.readableByteCount()];
                                    dataBuffers.read(content);
                                    responseData = new String(content, StandardCharsets.UTF_8);
                                } catch (Exception e) {
                                    log.info("获取返回值失败，失败原因：{}", Throwables.getStackTraceAsString(e));
                                } finally {
                                    DataBufferUtils.release(dataBuffers);
                                }
                                Object resJson;
                                try {
                                    resJson = JSONObject.parseObject(responseData);
                                } catch (Exception e) {
                                    resJson = responseData;
                                }

                                if (status != HttpStatus.OK) {
                                    log.debug("---HttpResponseFilter_filter status:{}", status);
                                    if (null != status) {
                                        if (null != resJson && resJson instanceof JSONObject &&
                                                ((JSONObject) resJson).containsKey("errMsg") && null != ((JSONObject) resJson).get("errMsg")) {
                                            responseData = JSONObject.toJSONString(resJson);
                                        } else {
                                            JsonResultVo resultVo = new JsonResultVo();
                                            resultVo.setMsgErr(status.getReasonPhrase());
                                            resultVo.setCode(status.value());
                                            responseData = JSONObject.toJSONString(resultVo);
                                        }
                                    }
                                }
                                if (!list.contains(uri))
                                    LogStashUtils.putGateWayLogtoLogStash(logName, originalRequest, resJson, exchange);
                                byte[] uppedContent = new String(responseData.getBytes(), StandardCharsets.UTF_8).getBytes();
                                getHeaders().setContentType(MediaType.APPLICATION_JSON);
                                return bufferFactory.wrap(uppedContent);
                            } else {
                                JSONObject resJson = new JSONObject();
                                String message = i18nUtil.getMessage("ERR.gatewayservice.httpResponse.toobig");
                                resJson.put("msg", message);
                                if (!list.contains(uri))
                                    LogStashUtils.putGateWayLogtoLogStash(logName, originalRequest, resJson, exchange);
                            }
                            return dataBuffers;
                        }));
                    }
                } else {
                    if (!list.contains(uri)) {
                        LogStashUtils.putGateWayLogtoLogStash(logName, originalRequest, null, exchange);
                        if (null == contentType)
                            log.info("---error--"+"------请求url：" + originalRequest.getURI().getPath() + " 返回值responseData为null，请注意调整------！");
                    }
                    if (null != contentType && contentType.toLowerCase().contains(MediaType.TEXT_HTML_VALUE) && status != HttpStatus.OK) {
                        Resource resource = findView(status);
                        if (null != resource) {
                            getHeaders().set(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_HTML_VALUE + ";charset=UTF-8");
                            try (InputStream in = resource.getInputStream()) {
                                int inLength = in.available();
                                byte[] buffer = new byte[inLength];
                                int len = IOUtils.read(in, buffer, 0, inLength);
                                String result = new String(buffer, 0, len);
                                return originalResponse.writeWith(DataBufferUtils.join((Publisher<DataBuffer>) body).map(dataBuffers -> {
                                    try {
                                        getHeaders().set(HttpHeaders.CONTENT_LENGTH, inLength + "");
                                        return bufferFactory.wrap(new String(result.getBytes(), StandardCharsets.UTF_8).getBytes());
                                    } catch (Exception e) {
                                        log.info("获取返回值失败，失败原因：{}", Throwables.getStackTraceAsString(e));
                                    }
                                    return dataBuffers;
                                }));
                            } catch (IOException e) {
                                log.info("---error--"+"读取异常页内容失败!", e);
                            }
                        }
                    }
                }
                return super.writeWith(body);
            }
        };
        return chain.filter(exchange.mutate().response(response).build());
    }


    @Override
    public int getOrder() {
        return -2;
    }

    protected Resource findView(HttpStatus status) {
        ArrayList<String> list = Lists.newArrayList(status.value() + ".html", SERIES_VIEWS.get(status.series()) + ".html");
        return list.stream()
                .map((Function<String, Resource>) s -> new ClassPathResource("static/error/" + s))
                .filter(Resource::exists)
                .findAny()
                .orElse(null);
    }
}
