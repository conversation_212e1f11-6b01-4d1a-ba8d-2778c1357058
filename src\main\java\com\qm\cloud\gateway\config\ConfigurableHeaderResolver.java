package com.qm.cloud.gateway.config;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.Set;

@Data
public class ConfigurableHeaderResolver implements KeyResolver {
	
	//定义从哪个header中取限流值,支持定义多个,默认从Authorization取
	private Set<String> HeaderkeyNames = new HashSet<String>(Lists.newArrayList("Authorization"));
	
	private static final String DEFAULT_HEAD_VALUE = "noneHeader";

	@Override
	public Mono<String> resolve(ServerWebExchange exchange) {
		String jwt;
		for(String headerKey : HeaderkeyNames){
			jwt = exchange.getRequest().getHeaders().getFirst(headerKey);
			if(!StringUtils.isEmpty(jwt))
				return Mono.just(jwt);
		}
		return Mono.just(DEFAULT_HEAD_VALUE);
	}
}