package com.qm.cloud.gateway.filter;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.qm.cloud.gateway.remote.UserRemote;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class BtTransCodeGatewayFilterFactory extends AbstractGatewayFilterFactory {

    @Autowired
    private I18nUtil i18nUtil;

    @Autowired
    private UserRemote userRemote;

    /**
     * 是否允许事务码为空
     */
    @Value("${jwt.auth.transcode:false}")
    private boolean transCodeAuth;

    @Override
    public GatewayFilter apply(Object config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            URI uri = request.getURI();
            log.debug("{} 接口事务码认证", uri.getPath());
            HttpHeaders headers = request.getHeaders();
            Map<String, String> paramap = new HashMap<>();
            headers.forEach((key, val) -> {
                paramap.put(key, headers.getFirst(key));
            });
            String transCode = headers.getFirst("transcode");
            log.debug("{} transCode", transCode);
            if (!BootAppUtil.isNullOrEmpty(transCode)) {
                //添加事务码验证
                String tenantId = headers.getFirst("tenantId");
                String companyId = headers.getFirst("companyId");
                String userId = headers.getFirst("userId");
                log.info("-----transCode--"+transCode+"--------path----"+uri.getPath());
                JsonResultVo menuResult = userRemote.validTransCodeAuth(tenantId, companyId, userId, transCode,uri.getPath());
                if (menuResult.getCode() == 200 && !Convert.toBool(menuResult.getData())) {
                    String message = i18nUtil.getMessage("ERR.gatewayservice.common.noauth");
                    return authorizeFailure(exchange, message);
                } else {
                    log.debug("userId:{} have {} auth!", userId, transCode);
                }
            } else if (transCodeAuth) {
                String message = i18nUtil.getMessage("ERR.gatewayservice.common.noauth");
                return authorizeFailure(exchange, message);
            }
            return chain.filter(exchange.mutate().request(request).build());
        }, 10);
    }

    /**
     * 认证失败，返回失败Mono
     *
     * @param exchange         HTTP交换机
     * @param responseErrorMsg 响应的错误信息
     * @return 包含错误信息的Mono
     */
    private Mono<Void> authorizeFailure(ServerWebExchange exchange, String responseErrorMsg) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        JsonResultVo res = new JsonResultVo();
        res.setMsgErr(responseErrorMsg);
        res.setCode(HttpStatus.UNAUTHORIZED.value());
        String body = JSON.toJSONString(res);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }
}
