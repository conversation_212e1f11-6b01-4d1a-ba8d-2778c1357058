package com.qm.cloud.gateway.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.remote.UserRemote;
import com.qm.cloud.gateway.service.AuthService;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/12/17$ 14:51$
 **/
@Slf4j
@Component
public class EpAuthorizeGatewayFilterFactory extends AbstractGatewayFilterFactory<EpAuthorizeGatewayFilterFactory.Config> {

    private static final String AUTHORIZE_TOKEN = "Authorization";
    private static final String AUTHORIZE_TOKEN_LOWER = "authorization";
    private static final String JWT_TOKEN = "jwt";

    @Autowired
    private AuthService authService;
    @Autowired
    private I18nUtil i18nUtil;


    public EpAuthorizeGatewayFilterFactory() {
        super(Config.class);
    }

    @SuppressWarnings("squid:S3776")
    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            URI uri = request.getURI();
            log.debug("{}接口认证", uri.getPath());
            HttpHeaders headers = request.getHeaders();
            Map<String, String> paramap = new HashMap<>();
            headers.forEach((key, val) -> {
                paramap.put(key, headers.getFirst(key));
            });
            request = exchange.getRequest().mutate().headers(headersParam -> headersParam.setAll(paramap)).build();

            // 导出放行，需要判断header里的属性
            String export = headers.getFirst(GatewayConstants.IGNOREAUTH);
            if (!BootAppUtil.isNullOrEmpty(export) && "true".equals(export)) {
                return chain.filter(exchange.mutate().request(request).build());
            }

            String token = headers.getFirst(AUTHORIZE_TOKEN);
            // 验证token为空，则没有访问权限
            if (BootAppUtil.isNullOrEmpty(token)) {
                token = headers.getFirst(AUTHORIZE_TOKEN_LOWER);
                if (BootAppUtil.isNullOrEmpty(token)) {
                    token = headers.getFirst(JWT_TOKEN);
                    if (BootAppUtil.isNullOrEmpty(token)) {
                        log.info("{}没有权限访问", request.getURI().getPath());
                        String message = i18nUtil.getMessage("ERR.gatewayservice.common.noauth");
                        return authorizeFailure(exchange, message);
                    }
                }
            }
            /**
             * log.debug("当前的token为：{}", token);
             */
            // 认证
            List<String> useragent = headers.get("useragent");
            String userType;
            if (CollUtil.isEmpty(useragent)) {
                String message = i18nUtil.getMessage("ERR.gatewayservice.epAuthorizeGatewayFilter.useragent");
                return authorizeFailure(exchange, message);
            } else {
                userType = useragent.toString();
            }
            JsonResultVo<IJwtInfo> result = null;
            try {
                result = authService.getUserInfoByJwt(token, userType, uri.getPath(), headers.getFirst(GatewayConstants.APPKEY));
            } catch (Exception e) {
                log.info("---error--"+"没有权限访问:" + e.getMessage(), e);
                String message = i18nUtil.getMessage("ERR.gatewayservice.common.noauth");
                return authorizeFailure(exchange, message);
            }
            // 认证失败
            if (result.getCode() == 500) {
                log.info("path：{}\nAuthService.getUserInfoByJwt(**)：{}\n", request.getURI().getPath(), result.getMsg());
                return authorizeFailure(exchange, result.getMsg());
            }
            return chain.filter(exchange.mutate().request(request).build());
        }, config.getOrder());
    }

    /**
     * 认证失败，返回失败Mono
     *
     * @param exchange         HTTP交换机
     * @param responseErrorMsg 响应的错误信息
     * @return 包含错误信息的Mono
     */
    private Mono<Void> authorizeFailure(ServerWebExchange exchange, String responseErrorMsg) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        JsonResultVo res = new JsonResultVo();
        res.setMsgErr(responseErrorMsg);
        res.setCode(HttpStatus.UNAUTHORIZED.value());
        String body = JSON.toJSONString(res);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    @Data
    public static class Config {
        // 排序
        private int order;
    }

}
