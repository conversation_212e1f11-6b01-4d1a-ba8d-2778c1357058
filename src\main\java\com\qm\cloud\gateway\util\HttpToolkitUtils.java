package com.qm.cloud.gateway.util;

import com.alibaba.fastjson.JSON;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Request.Builder;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 
 * @Description  Http工具类
 * <AUTHOR>
 * @Date 2018年8月22日
 */
public class HttpToolkitUtils {
	
	private static Logger logger = LoggerFactory.getLogger(HttpToolkitUtils.class);

	private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(5000L, TimeUnit.MILLISECONDS)
            .readTimeout(5000L, TimeUnit.MILLISECONDS)
            .writeTimeout(5000L, TimeUnit.MILLISECONDS)
            .hostnameVerifier(SslUtil.DO_NOT_VERIFY)
            .sslSocketFactory(SslUtil.trustAllHosts(),new MyX509TrustManager())
            //其他配置
           .build();
	
	public static OkHttpClient getInstance() {
		return okHttpClient;
	}
	
	private HttpToolkitUtils() {
		
	}

	/**
	 * get 请求，返回响应字符串
	 * @param url	链接
	 * @param param  字符串，格式 a=b&b=d
	 * @param header header信息
	 * @return
	 */
	public static HttpResponse sendDataAndGet(String url,String param,Map<String,String> header){
		HttpResponse httpResp=new HttpResponse();
		try {
			String parameter=urlEncodeStr(param,"utf-8");
			if(parameter!=null) {
				 url=url+"?"+parameter;
			}
			logger.info("uri:"+url);
			OkHttpClient client=getInstance();
			Builder build=new Builder();
			build.url(url);
			build.get();
			if(header!=null) {
				Iterator<String> iter = header.keySet().iterator();
				while(iter.hasNext()) {
					String key = iter.next();
					String val = header.get(key);
					build.addHeader(key, val);
				}
			}
			Request request = build.build();
			logger.info("HttpToolkitUtils sendDataAndGet headers:{}", request.headers());
			Response response = client.newCall(request).execute();
			String result=response.body().string();
			logger.info("result:"+result.substring(0, result.length()>300?300:result.length()));
			httpResp.setCode(response.code());
			httpResp.setSuccess(true);
			httpResp.setResult(result);
			return httpResp;
		} catch (Exception e) {
			// TODO: handle exception
			logger.error("error",e);
			httpResp.setSuccess(false);
			httpResp.setResult(e.getMessage());
		}
		
		return httpResp;
	}
	
	/**
	 * GET 请求 ，序列化响应值
	 * @param url 链接
	 * @param param	字符串，格式 a=b&b=d
	 * @param header header信息
	 * @param t 反序列化对象
	 * @return
	 */
	public static <T>T sendDataAndGet(String url,String param,Map<String,String> header,Class<T> t) {
		try {
			HttpResponse resp=sendDataAndGet(url,param,header);
			if(!resp.isSuccess()||resp.getCode()!=200) {
				return null;
			}
			T obj=JSON.parseObject(resp.getResult(), t);
			return obj;
		} catch (Exception e) {
			// TODO: handle exception
			logger.error("error",e);
		}
		return null;
	}
	
	
	/**
     * get请求的参数编码格式转换 
     * @param param  字符串，格式a=b&b=d
     * @param code 编码格式
     * @return 
     * @throws UnsupportedEncodingException
     */
    public static String urlEncodeStr(String param,String code) throws UnsupportedEncodingException{
    	if(param==null) {
    		return null;
    	}
		StringBuilder sb=new StringBuilder();
		String[] arr=param.split("&");
		for(int i=0;i<arr.length;i++){
			String[] arr2=arr[i].split("=");
			if(arr2.length>1){
				sb.append(arr2[0]);
				String a=URLEncoder.encode(arr2[1], code);
				sb.append("=");
				sb.append(a);
				if(i<arr.length-1){
					sb.append("&");
				}
			}		
		}
		return sb.toString();
	}
	
    /**
     * map数据 格式并编码为 a=b&b=d
     * @param map
     * @param code 编码格式
     * @return
     */
	public static String urlEncodeMap(Map<String,Object> map,String code) {
			if(map==null) {
				return null;
			}
			StringBuilder sb=new StringBuilder();
			Iterator<String> iter = map.keySet().iterator();
			while(iter.hasNext()) {
				String key = iter.next();
				Object val = map.get(key);
				String value =String.valueOf(val);
				if("".equals(value)||"null".equals(value)) {
					continue;
				}
				sb.append(key+"=");
				sb.append(value+"&");
			}
		return sb.substring(0, sb.length()-1);
	}
    
    
}
