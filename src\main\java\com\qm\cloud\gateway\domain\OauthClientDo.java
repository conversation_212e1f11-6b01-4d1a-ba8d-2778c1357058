package com.qm.cloud.gateway.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @Title: OauthClientDo
 * @Description:OauthClientDo
 * @date 2019/11/25
 */

@Data
public class OauthClientDo {
    //客户端编号
    private String clientId;
    //客户端能访问的资源id集合
    private String resourceIds;
    //app_secret
    private String clientSecret;
    //指定client的权限范围
    private String scope;
    //授权码模式
    private String authorizedGrantTypes;
    //客户端重定向uri
    private String webServerRedirectUri;
    //指定用户的权限范围
    private String authorities;
    //设置access_token的有效时间(秒)
    private Integer accessTokenValidity;
    //设置refresh_token有效期(秒)
    private Integer refreshTokenValidity;
    //必须是json格式
    private String additionalInformation;
    //authorization_code模式
    private String autoapprove;
    //主键
    private Integer id;
    //客户端名称
    private String clientName;
    //客户端描述
    private String clientDesc;
    //停用标志
    private String stop;
    //停用日期
    private String stopTime;
    //创建者
    private String createBy;
    //创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    private Timestamp createOn;
    //更新者
    private String updateBy;
    //更新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    private Timestamp updateOn;
    //时间戳
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss:SSS")
    private Timestamp dtstamp;
    //grant_type为authorization_code 时使用默认为空：用户名密码登录
    private String authorizationCodeAuthType;
}
