package com.qm.cloud.gateway.config;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.qm.cloud.gateway.constant.GatewayConstants;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "jwt.expire")
@Data
public class JwtExpireConfig {

    private String base;
    private String customization;
    private static volatile Map<String, Long> customExpire;
    private static volatile Map<String, Long> baseExpire;

    public Long getJwtExpire(String clientId, String userSource) {
        try {
            Long expire = 0L;
            if (customExpire == null || baseExpire == null) {
                synchronized (JwtExpireConfig.class) {
                    customExpire = JSON.parseObject(customization, new TypeReference<HashMap<String, Long>>() {
                    });
                    baseExpire = JSON.parseObject(base, new TypeReference<HashMap<String, Long>>() {
                    });
                }
            }
            if (userSource.equals(GatewayConstants.CUSTOM)) {
                expire = customExpire.get(clientId);
                if (null == expire || expire == 0L)
                    expire = customExpire.get(GatewayConstants.DEFAULT);
            } else {
                expire = baseExpire.get(clientId);
                if (null == expire || expire == 0L)
                    expire = baseExpire.get(GatewayConstants.DEFAULT);
            }
            return expire;
        } catch (Exception ex) {
            return Convert.toLong(base);
        }
    }

    public Long getCustomExpire(String clientId) {
        if (customExpire == null) {
            synchronized (JwtExpireConfig.class) {
                if (customExpire == null) {
                    customExpire = JSON.parseObject(customization, new TypeReference<HashMap<String, Long>>() {
                    });
                }
            }
        }
        return customExpire.get(clientId);
    }
}
