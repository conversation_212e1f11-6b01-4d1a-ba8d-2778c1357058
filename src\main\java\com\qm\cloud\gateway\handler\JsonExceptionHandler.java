package com.qm.cloud.gateway.handler;

import com.netflix.hystrix.exception.HystrixRuntimeException;
import com.qm.cloud.gateway.config.properties.LogProperties;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.util.LogStashUtils;
import com.qm.tds.api.util.SpringContextHolder;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.web.ErrorProperties;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.DefaultErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.cloud.gateway.support.NotFoundException;
import org.springframework.cloud.gateway.support.TimeoutException;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.*;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * @description: 自定义异常处理
 * @author: Cyl
 * @time: 2020/9/2 15:50
 */
@Slf4j
public class JsonExceptionHandler extends DefaultErrorWebExceptionHandler {

    @Autowired
    private LogProperties logProperties;

    private static final Map<HttpStatus.Series, String> SERIES_VIEWS;
    private final ErrorProperties errorProperties;

    static {
        Map<HttpStatus.Series, String> views = new EnumMap<>(HttpStatus.Series.class);
        views.put(HttpStatus.Series.CLIENT_ERROR, "4xx");
        views.put(HttpStatus.Series.SERVER_ERROR, "5xx");
        SERIES_VIEWS = Collections.unmodifiableMap(views);
    }

    public JsonExceptionHandler(ErrorAttributes errorAttributes, WebProperties.Resources resources, ErrorProperties errorProperties, ApplicationContext applicationContext) {
        super(errorAttributes, resources, errorProperties, applicationContext);
        this.errorProperties = errorProperties;
    }

    /**
     * 获取异常属性
     */
    @SuppressWarnings("squid:S3776")
    protected Map<String, Object> getErrorAttributes(ServerRequest request, boolean includeStackTrace) {
        Map<String, Object> attributes = request.attributes();
        int code = HttpStatus.INTERNAL_SERVER_ERROR.value();
        String msg = null;
        Map<String, Object> map = new HashMap<>();
        Throwable error = super.getError(request);
        if (error instanceof NotFoundException) {
            code = HttpStatus.NOT_FOUND.value();
        } else if (error instanceof ResponseStatusException) {
            ResponseStatusException exception = (ResponseStatusException) error;
            code = exception.getStatusCode().value();
            if (code == GatewayConstants.ERRORCODE) {
                String path = request.uri().getPath();
                //判读断言
                I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.responseStatus", path);
                map = response(code, msg);
            }
        } else if (error instanceof java.lang.NullPointerException) {
            if (error.getMessage() != null && "The iterator returned a null value".equals(error.getMessage())) {
                I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.resourceNotFound");
                map = response(code, msg);
            } else {
                I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
                msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.nullPointer");
                map = response(code, msg);
            }
        }
        // 熔断超时
        else if (error instanceof TimeoutException) {
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.timeout");
            map = response(code, msg);
        }
        // 找不到服务
        else if (error instanceof HystrixRuntimeException && error.getCause() instanceof NotFoundException) {
            msg = error.getCause().getMessage();
            map = response(code, error.getCause().getMessage());
        }
        // todo 上传文件失败 这种响应不是很好，以后有更好的方法可以优化
        else if (error instanceof HystrixRuntimeException &&
                "multipart".equals(request.headers().contentType().orElse(new MediaType("other")).getType())) {
            String systemName = Arrays.stream(request.uri().getPath().split("/"))
                    .filter(StringUtils::isNotEmpty)
                    .findFirst()
                    .orElse("");
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.hystrixRuntime", systemName);
            map = response(code, msg);
        }
        if (BootAppUtil.isNullOrEmpty(msg)) {
            I18nUtil i18nUtil = SpringContextHolder.getBean(I18nUtil.class);
            msg = i18nUtil.getMessage("ERR.gatewayservice.exHandler.isNullOrEmpty");
            map = response(code, msg + error.getMessage());
        }
        // 保存ELK日志
        if ("true".equals(logProperties.getEnabled())) {
            LogStashUtils.putGateWayException(logProperties.getLogName(), map, attributes);
        }
        return map;
    }

    /**
     * 指定响应处理方法为JSON处理的方法
     *
     * @param errorAttributes
     */
    @Override
    protected RouterFunction<ServerResponse> getRoutingFunction(ErrorAttributes errorAttributes) {
        // 现在默认返回JSON格式的错误，没有找到好的方法找到request，所以无法判断当前的content-type，如果之后可以取到，应判断类型是否是json，
        // 不是则返回下面注释的VIEW方法
        // RouterFunctions.route(RequestPredicates.all(), this::renderErrorView)
        return RouterFunctions.route(RequestPredicates.all(), this::renderErrorResponse);
    }

    /**
     * 根据code获取对应的HttpStatus
     *
     * @param errorAttributes
     * @return
     */
    @Override
    protected int getHttpStatus(Map<String, Object> errorAttributes) {
        Object codeObj = errorAttributes.get("code");
        int statusCode = (codeObj instanceof Integer) ? (Integer) codeObj : 500;
        Object msgObj = errorAttributes.get("msg");
        String msg = msgObj != null ? msgObj.toString() : "";
        if (msg.startsWith("您上传的附件")) {
            return 200;
        } else {
            return statusCode;
        }
    }

    /**
     * 构建返回的JSON数据格式
     *
     * @param status       状态码
     * @param errorMessage 异常信息
     * @return
     */
    public static Map<String, Object> response(int status, String errorMessage) {
        Map<String, Object> res = new HashMap<>(4);
        res.put("status", "E");
        res.put("code", status);
        res.put("msg", errorMessage);
        res.put("ok", false);
        return res;
    }


    @Override
    protected Mono<ServerResponse> renderErrorView(ServerRequest request) {
        boolean includeStackTrace = isIncludeStackTrace(request, MediaType.TEXT_HTML);
        Map<String, Object> error = getErrorAttributes(request, includeStackTrace);
        int errorStatus = getHttpStatus(error);
        ServerResponse.BodyBuilder responseBody = ServerResponse.status(errorStatus)
                .contentType(MediaType.TEXT_HTML);
        Exception e = null;
        return Flux
                .just("/error/" + errorStatus,
                        "/error/" + SERIES_VIEWS.get(errorStatus),
                        "/error/error")
                .flatMap((viewName) ->
                        renderErrorView(viewName, responseBody, error))
                .switchIfEmpty(errorProperties.getWhitelabel().isEnabled()
                        ? renderDefaultErrorView(responseBody, error)
                        : Mono.error(getError(request)))
                .next().doOnNext((response) -> logError(request, response,e));
    }

}
