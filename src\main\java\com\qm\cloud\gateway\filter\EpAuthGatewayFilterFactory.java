package com.qm.cloud.gateway.filter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.jwt.JwtInfo;
import com.qm.cloud.gateway.util.JwtTokenUtil;
import com.qm.cloud.gateway.util.LogStashUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.JSONUtils;
import com.qm.tds.util.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.filter.factory.rewrite.RewriteFunction;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseCookie;
import org.springframework.http.client.reactive.ClientHttpResponse;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/12/17$ 14:51$
 **/
@Slf4j
@Component
public class EpAuthGatewayFilterFactory extends AbstractGatewayFilterFactory<EpAuthGatewayFilterFactory.Config> {

    private static final String REPBODY_USERID = "userId";
    @Autowired
    private I18nUtil i18nUtil;

    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();

    @Override
    public List<String> shortcutFieldOrder() {
        return Arrays.asList("userIdField", "kickAble", "authType", "order");
    }

    public EpAuthGatewayFilterFactory() {
        super(Config.class);
    }

    private class DnyRewrite implements RewriteFunction<Object, Object> {
        private Config config;

        public DnyRewrite(Config config) {
            this.config = config;
        }

        @Override
        public Publisher<Object> apply(ServerWebExchange exchange, Object originalBody) {
            String originalBodystr = (String) originalBody;
            log.debug("original response=" + originalBodystr);
            // 获取请求对象，手机登录请求使用
            Object reqBody = exchange.getAttributes().get(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY);
            List<String> useragent = exchange.getRequest().getHeaders().get(GatewayConstants.USERAGENT);
            String userType = "";
            if (CollUtil.isNotEmpty(useragent)) {
                userType = useragent.toString();
            }
            JsonResultVo jwtAuth = genJwtAuthentication(reqBody, originalBodystr, config, "", userType);
            String moidifiedBody = JSONUtil.toJsonStr(jwtAuth);
            return Mono.just(moidifiedBody);
        }
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
            ServerHttpResponse originalResponse = exchange.getResponse();
            /**
             * ServerHttpRequest request = exchange.getRequest();
             * ServerHttpRequest originalRequest = exchange.getRequest();
             * DataBufferFactory bufferFactory = originalResponse.bufferFactory();
             */
            ServerHttpResponseDecorator responseDecorator = new ServerHttpResponseDecorator(originalResponse) {
                @Override
                public Mono<Void> writeWith(Publisher<? extends DataBuffer> body) {
                    Class inClass = String.class;
                    Class outClass = String.class;
                    MediaType originalResponseContentType;
                    Object contentType = exchange.getAttribute(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR);
                    assert contentType != null;
                    if (String.class.isAssignableFrom(contentType.getClass())) {
                        originalResponseContentType = MediaType.valueOf((String) contentType);
                    } else {
                        originalResponseContentType = exchange.getAttribute(ServerWebExchangeUtils.ORIGINAL_RESPONSE_CONTENT_TYPE_ATTR);
                    }

                    HttpHeaders httpHeaders = new HttpHeaders();
                    httpHeaders.setContentType(originalResponseContentType);
                    ClientResponse clientResponse = prepareClientResponse(exchange,body, httpHeaders);

                    Mono<Object> modifiedBody = clientResponse.bodyToMono(inClass)
                            .flatMap(originalBody -> new DnyRewrite(config).apply(exchange, originalBody));

                    BodyInserter bodyInserter = BodyInserters.fromPublisher(modifiedBody, outClass);
                    CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange,
                            exchange.getResponse().getHeaders());

                    return bodyInserter.insert(outputMessage, new BodyInserterContext())
                            .then(Mono.defer(() -> {
                                /**
                                 * long contentLength1 = getDelegate().getHeaders().getContentLength();
                                 */
                                Flux<DataBuffer> messageBody = outputMessage.getBody();
                                HttpHeaders headers = getDelegate().getHeaders();
                                if (!headers.containsKey(HttpHeaders.TRANSFER_ENCODING)) {
                                    messageBody = messageBody.doOnNext(data -> headers.setContentLength(data.readableByteCount()));
                                }
                                return getDelegate().writeWith(messageBody);
                                //TODO gateway 堆栈溢出异常的解决
                            }))
                            .onErrorResume(throwable -> {
                                log.info("---error--"+"执行url" + exchange.getRequest().getURI() + "EpAuthGatewayFilterFactory-apply-BodyInserter异常!");
                                return outputMessage.getBody().map(DataBufferUtils::release).then(chain.filter(exchange));
                            });
                }

                @Override
                public Mono<Void> writeAndFlushWith(Publisher<? extends Publisher<? extends DataBuffer>> body) {
                    return writeWith(Flux.from(body).flatMapSequential(p -> p));
                }
            };
            return chain.filter(exchange.mutate().response(responseDecorator).build());
        }, config.getOrder());
    }

    private ClientResponse prepareClientResponse(ServerWebExchange exchange, Publisher<? extends DataBuffer> body, HttpHeaders httpHeaders) {
        ClientResponse.Builder builder;
        builder = ClientResponse.create(exchange.getResponse().getStatusCode(), messageReaders);
        return builder.headers(headers -> headers.putAll(httpHeaders)).body(Flux.from(body)).build();
    }

    public class ResponseAdapter implements ClientHttpResponse {

        private final Flux<DataBuffer> flux;
        private final HttpHeaders headers;

        public ResponseAdapter(Publisher<? extends DataBuffer> body, HttpHeaders headers) {
            this.headers = headers;
            if (body instanceof Flux) {
                flux = (Flux) body;
            } else {
                flux = ((Mono) body).flux();
            }
        }

        @Override
        public Flux<DataBuffer> getBody() {
            return flux;
        }

        @Override
        public HttpHeaders getHeaders() {
            return headers;
        }

        @Override
        public HttpStatus getStatusCode() {
            return null;
        }

        @Override
        public int getRawStatusCode() {
            return 0;
        }

        @Override
        public MultiValueMap<String, ResponseCookie> getCookies() {
            return null;
        }
    }

    /**
     * 对应 配置管理-网关配置-过滤器配置-参数
     * EpAuth=[userIdField],[kickAble],[authType],[order]
     * 例如：EpAuth=id,false,epc,-5
     */
    @Data
    public static class Config {
        // 指定表示用户ID的字段, 默认userId。
        private String userIdField = REPBODY_USERID;
        // 返回的jwt是否互踢，默认不互踢。
        private boolean kickAble = false;
        // 登陆方式，默认用户名密码
        private String authType = "username";
        // 排序
        private int order;
    }

    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    private RedisUtils redisUtils;

    @SuppressWarnings("squid:S3776")
    public JsonResultVo genJwtAuthentication(Object reqBody, String repBody, Config config, String clientId, String userType) {
        try {
            String jwt = "";
            JsonResultVo<Map<String, Object>> bodyFmt = JSON.parseObject(repBody, new TypeReference<JsonResultVo<Map<String, Object>>>() {
            });
            Map<String, Object> data = bodyFmt.getData();

            log.info("-----EpAuth------data-----",data);
            if (data == null && BootAppUtil.isnotNullOrEmpty(bodyFmt.getMsg()))
                return bodyFmt;
            if (HttpStatus.OK.value() != bodyFmt.getCode()) {
                log.info("---error--"+"生成jwt失败, resultCode:{}", bodyFmt.getCode());
                return bodyFmt;
            }
            // 判断如果是手机号验证码登陆，判断验证码
            String authType = config.getAuthType();
            String userSource = "";
            Object userId = "";
            String userName = "";
            String loginName = "";
            if ("vc".equals(config.getAuthType())) {
                JsonResultVo telResult = telAuth(reqBody, data);
                if (telResult.getCode() != 200) {
                    return telResult;
                }
            } else if ("dms".equals(config.getAuthType())) {
                userSource = GatewayConstants.CUSTOM;
            }
            if (data != null) {
                userId = data.get(config.getUserIdField());
                // 用户ID加登陆时间
                if (GatewayConstants.APPKEY.equals(config.getAuthType())) {
                    Object secretObj = data.get(GatewayConstants.SECRETKEY);
                    if (null == secretObj || ((JSONArray) secretObj).isEmpty()) {
                        log.info("---error--"+"生成jwt失败,未找到有效的appkey和secretkey, resultCode:{}", bodyFmt.getCode());
                        return bodyFmt;
                    }
                    if (!BootAppUtil.isNullOrEmpty(reqBody)) {
                        Map map = JSONUtils.jsonToMap(reqBody.toString());
                        if (null != map.get(GatewayConstants.LOGINTIMESTAMP)) {
                            userId = ((JSONObject) (((JSONArray) secretObj).get(0))).get(config.getUserIdField());
                            userName = map.get(GatewayConstants.LOGINTIMESTAMP).toString();
                            loginName = userId.toString();
                        }
                    }
                } else {
                    userName = data.get(GatewayConstants.LOGINTIMESTAMP).toString();
                }
            }
            if (BootAppUtil.isNullOrEmpty(userId)) {
                log.info("---error--"+"生成jwt失败,{}为空", REPBODY_USERID);
                bodyFmt.setCode(500);
                bodyFmt.setData(null);
                String message = i18nUtil.getMessage("ERR.gatewayservice.common.generateJwtFail");
                bodyFmt.setMsg(message);
                return bodyFmt;
            }

            JsonResultVo<Map<String, String>> jwtParam = JSON.parseObject(repBody, new TypeReference<JsonResultVo<Map<String, String>>>() {
            });
            IJwtInfo info = new JwtInfo(userName, userId.toString(), jwtParam.getData().get("vrealname"),
                    null, null, null, false, jwtParam.getData(), "");

            // 直接根据用户信息返回jwt
            jwt = jwtTokenUtil.generateToken(info, userType, userSource);
            // 根据jwt缓存用户信息
            if (!BootAppUtil.isNullOrEmpty(info.getUniqueName()))
                loginName = info.getUniqueName();
            long expire = jwtTokenUtil.getExpire(userType, userSource);
            //数据存储key
            String jwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, userType, jwt);
            redisUtils.set(jwtKey, info, expire);
            log.debug("--缓存信息jwt{}", jwt);
            // 根据用户账号缓存有效的jwt
            String loginNameKey = redisUtils.keyBuilder(GatewayConstants.LOGINNAME, userType, loginName);
            redisUtils.set(loginNameKey, jwt, expire);
            log.debug("--用登录账号缓存有效的jwt,loginName={},expire={}", loginName, expire);
            Map<String, Object> map = new HashMap<>();
            map.put("userInfo", data);
            map.put("token", jwt);
            JsonResultVo resp = new JsonResultVo();
            resp.setData(map);
            return resp;
        } catch (Exception e) {
            log.info("---error--"+"生成jwt失败:" + e.getMessage(), e);
            JsonResultVo resp = new JsonResultVo();
            resp.setCode(500);
            String message = i18nUtil.getMessage("ERR.gatewayservice.common.generateJwtFail");
            resp.setMsg(message);
            return resp;
        }
    }

    private JsonResultVo telAuth(Object reqBody, Map<String, Object> data) {
        JsonResultVo bodyFmt = new JsonResultVo<>();
        Object phone = data.get("vmobile");
        String mobile = !BootAppUtil.isNullOrEmpty(phone) ? phone.toString() : "";
        String key = redisUtils.keyBuilder("msg", "sms", "login", mobile);
        Object code = redisUtils.get(key);
        if (BootAppUtil.isNullOrEmpty(code)) {
            bodyFmt.setCode(500);
            bodyFmt.setData(null);
            String message = i18nUtil.getMessage("ERR.gatewayservice.common.sendCodeAgain");
            bodyFmt.setMsg(message);
            return bodyFmt;
        }
        if (!BootAppUtil.isNullOrEmpty(reqBody)) {
            Map telMap = JSONUtils.jsonToMap(reqBody.toString());
            if (!code.equals(telMap.get("msgCode"))) {
                bodyFmt.setCode(500);
                bodyFmt.setData(null);
                String message = i18nUtil.getMessage("ERR.gatewayservice.common.codeWrong");
                bodyFmt.setMsg(message);
                return bodyFmt;
            }
        } else {
            bodyFmt.setCode(500);
            bodyFmt.setData(null);
            String message = i18nUtil.getMessage("ERR.gatewayservice.common.paramWrong");
            bodyFmt.setMsg(message);
            return bodyFmt;
        }
        return bodyFmt;
    }
}
