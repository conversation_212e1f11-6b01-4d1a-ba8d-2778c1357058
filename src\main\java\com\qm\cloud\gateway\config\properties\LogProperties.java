package com.qm.cloud.gateway.config.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class LogProperties {

    /**
     * 是否开启记录ELK日志
     */
    @Value("${logs.enabled}")
    private String enabled;

    /**
     * 不记录日志API
     */
    @Value("${logs.ignoreLogUri}")
    private String ignoreLogUri;

    /**
     * 日志文件名
     */
    @Value("${logs.name}")
    private String logName;
}