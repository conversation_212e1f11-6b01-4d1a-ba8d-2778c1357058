package com.qm.cloud.gateway.filter;

import com.qm.cloud.gateway.util.LogStashUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;


import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/1/6$ 16:40$
 **/
@Component
@Slf4j
public class HttpRequestFilter implements GlobalFilter, Ordered {
    public static final String FILE_UPLOAD_REQUEST_BODY = "文件上传";
    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();

    @Autowired
    ServerCodecConfigurer codecConfigurer;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        log.debug("[-HttpRequestFilter-]接口请求.filter:uri={}", request.getURI());
        HttpMethod method = request.getMethod();
        MediaType contentType = request.getHeaders().getContentType();
        exchange.getAttributes().put(LogStashUtils.START_TIME, System.currentTimeMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        exchange.getAttributes().put(LogStashUtils.ACCESS_TIME, simpleDateFormat.format(new Date()));
        // 储存header
        exchange.getAttributes().put(LogStashUtils.HTTP_REQUEST, request);
        if (null != contentType && HttpMethod.POST.equals(method)) {
            if (contentType.includes(MediaType.APPLICATION_JSON) ||
                    contentType.includes(MediaType.APPLICATION_FORM_URLENCODED) ||
                    contentType.includes(MediaType.TEXT_PLAIN)) {
                return getVoidMono(exchange, chain);
            } else if (contentType.includes(MediaType.MULTIPART_FORM_DATA)) {
                exchange.getAttributes().put(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY, FILE_UPLOAD_REQUEST_BODY);
            }
        } else {
            exchange.getAttributes().put(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY, "" + request.getQueryParams());
        }
        return chain.filter(exchange);
    }

    private Mono<Void> getVoidMono(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerRequest serverRequest = ServerRequest.create(exchange, codecConfigurer.getReaders());
        // 读取请求体
        Mono<String> modifiedBody = serverRequest.bodyToMono(String.class)
                .flatMap(body -> {
                    handleRequestBody(exchange, body);
                    return Mono.just(body);
                });
        BodyInserter<Mono<String>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(modifiedBody, String.class);
        HttpHeaders headers = new HttpHeaders();
        headers.putAll(exchange.getRequest().getHeaders());
        CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, headers);
        return bodyInserter.insert(outputMessage, new BodyInserterContext())
                .then(Mono.defer(() -> {
                    ServerHttpRequestDecorator decorator = new ServerHttpRequestDecorator(exchange.getRequest()) {
                        @Override
                        public Flux<DataBuffer> getBody() {
                            return outputMessage.getBody();
                        }
                    };
                    return chain.filter(exchange.mutate().request(decorator).build());
                }))
                .onErrorResume(throwable -> {
                    log.info("---error--"+"执行url" + serverRequest.uri() + "HttpRequestFilter-getVoidMono-BodyInserter异常：" + throwable.getMessage());
                    return outputMessage.getBody().map(DataBufferUtils::release).then(chain.filter(exchange));
                });
    }

    private void handleRequestBody(ServerWebExchange exchange, String bodyStr) {
        if (bodyStr != null) {
            if (bodyStr.startsWith("{") && bodyStr.endsWith("}")) {
                exchange.getAttributes().put(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY, bodyStr);
            } else {
                exchange.getAttributes().put(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY, "{" + bodyStr + "}");
            }
        } else {
            exchange.getAttributes().put(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY, "");
        }
    }

    @Override
    public int getOrder() {
        return -2;
    }
}
