package com.qm.cloud.gateway.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.qm.cloud.gateway.domain.GatewayFilterDO;
import com.qm.cloud.gateway.domain.GatewayRouteVO;
import com.qm.cloud.gateway.remote.GatewayAdminRemote;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.filter.FilterDefinition;
import org.springframework.cloud.gateway.handler.predicate.PredicateDefinition;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionRepository;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import jakarta.annotation.Resource;
import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GatewayService implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher publisher;
    @Autowired
    private I18nUtil i18nUtil;
    @Resource
    private RouteDefinitionRepository routeDefinitionWriter;
    @Autowired
    @Lazy
    private GatewayAdminRemote gatewayAdminRemote;

    private static final List<String> NOT_SHORTCUT_FACTORIES = Lists.newArrayList("requestratelimiter", "retry", "hystrix");

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        publisher = applicationEventPublisher;
    }

    /**
     * 启动时加载路由，或主动调用刷新路由
     *
     * @throws Exception 未知异常
     */
    public void refresh() {
        log.info("reload routes...");
        JsonResultVo<GatewayRouteVO> routes = gatewayAdminRemote.getGagewayRoutes();
        if (routes.getCode() == HttpStatus.OK.value() && !CollectionUtils.isEmpty(routes.getDataList())) {
            log.info("共加载{}个路由!", routes.getDataList().size());
            reloadRouteDefinitions(routes.getDataList());

        } else {
            log.info("路由不存在!");
            String message = i18nUtil.getMessage("ERR.gatewayservice.gatewayService.refresh");
            throw new QmException(message);
        }
        log.info("reload routes completed!");
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }

    private void reloadRouteDefinitions(List<GatewayRouteVO> routes) {
        List<String> routeIds = routes.stream().map(gr -> String.valueOf(gr.getRouteName())).collect(Collectors.toList());
        //del expiration route definition.
        routeDefinitionWriter.getRouteDefinitions().subscribe(routeDef -> {
            if (!routeIds.contains(routeDef.getId())) {
                log.debug("del route {}", routeDef.getId());
                routeDefinitionWriter.delete(Mono.just(routeDef.getId())).subscribe();
            }
        });
        //insert and update route definition.
        routes.stream().map(this::convertToRouteDefinition).filter(Objects::nonNull).forEach(rd -> {
            routeDefinitionWriter.save(Mono.just(rd)).subscribe();
        });
    }

    private RouteDefinition convertToRouteDefinition(GatewayRouteVO gr) {
        RouteDefinition routeDefinition = new RouteDefinition();
        try {
            routeDefinition.setId(String.valueOf(gr.getRouteName()));
            List<PredicateDefinition> predicates = gr.getPredicateList().stream()
                    .map(c -> new PredicateDefinition(c.getDefinition()))
                    .collect(Collectors.toList());
            List<FilterDefinition> filters = gr.getFilterList().stream()
                    .map(this::convert)
                    .collect(Collectors.toList());
            routeDefinition.setPredicates(predicates);
            routeDefinition.setFilters(filters);
            routeDefinition.setOrder(gr.getRouteOrder());
            URI uri = format(gr.getUri());
            if (uri.getScheme() == null) {
                log.info("---error--"+"[{}]路由设置不正确，scheme can not be empty。", routeDefinition.getId());
                return null;
            }
            routeDefinition.setUri(uri);
            if (CollectionUtils.isEmpty(predicates)) {
                log.info("---error--"+"[{}]没有配置断言。", routeDefinition.getId());
                return null;
            }
            if (CollectionUtils.isEmpty(filters)) {
                log.warn("[{}]没有配置过滤器，请核对是否正常。", routeDefinition.getId());
            }
        } catch (Exception e) {
            log.info("---error--"+"convertToRouteDefinition error!! GatewayRoute:{} msg:{}", gr.toString(), e.getMessage());
            return null;
        }
        return routeDefinition;
    }

    private FilterDefinition convert(GatewayFilterDO gf) {
        if (StringUtils.isNotBlank(gf.getType()) && NOT_SHORTCUT_FACTORIES.contains(gf.getType().toLowerCase())) {
            FilterDefinition filterDefinition = new FilterDefinition();
            filterDefinition.setName(gf.getType());
            filterDefinition.setArgs(JSON.parseObject(gf.getDefinition(), new TypeReference<HashMap<String, String>>() {
            }));

            return filterDefinition;
        } else {
            return new FilterDefinition(gf.getDefinition());
        }
    }

    private URI format(String uri) {
        if (uri.startsWith("http")) {
            return UriComponentsBuilder.fromHttpUrl(uri).build().toUri();
        } else {
            return UriComponentsBuilder.fromUriString(uri).build().toUri();
        }
    }
}
