package com.qm.cloud.gateway.domain.DTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 在线用户信息
 *
 * <AUTHOR>
 * @date 2021/3/22 16:26
 */
@Schema(description = "在线用户信息")
@Data
public class OnlineUserDTO {
    @Schema(description = "终端")
    private String terminal;
    @Schema(description = "会话 ID")
    private String sessionId;
    @Schema(description = "用户 id")
    private String userId;
    @Schema(description = "在线时间")
    private Date onlineTime;
    @Schema(description = "数据JWT")
    private String jwt;
}
