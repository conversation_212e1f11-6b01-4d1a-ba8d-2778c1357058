package com.qm.cloud.gateway.controller;

import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询过滤器
 *
 * <AUTHOR>
 * @since 2021/4/12 9:23
 */
@RestController
@RequestMapping("/wycfilter")
@Slf4j
public class GatewayFilterController implements ApplicationContextAware, DisposableBean {
    @Autowired
    private I18nUtil i18nUtil;

    @GetMapping("/getAllFilter")
    @Operation(summary = "获取所有系统网关过滤器", description = "获取所有系统网关过滤器[author:10027705]")
    public JsonResultVo getAllFilter() {
        Map<String, AbstractGatewayFilterFactory> beansOfType = applicationContext.getBeansOfType(AbstractGatewayFilterFactory.class);
        Map<String, String> collect = beansOfType.entrySet().stream().collect(
                HashMap::new,
                (target, source) -> target.put(source.getKey(), source.getValue().name()),
                HashMap::putAll);
        JsonResultVo result = new JsonResultVo();
        result.setCode(HttpStatus.OK.value());
        String message = i18nUtil.getMessage("MSG.gatewayservice.getAllFilter.success");
        result.setMsg(message);
        result.setData(collect);
        return result;
    }

    private ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的context注入函数, 将其存入静态变量.
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void destroy() {
        applicationContext = null;
    }
}
