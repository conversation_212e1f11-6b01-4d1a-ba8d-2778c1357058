package com.qm.cloud.gateway.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 
 * @Description http响应结果封装类
 * <AUTHOR>
 * @Date 2018年8月21日
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HttpResponse {
	/**
	 * 响应code
	 */
	private Integer code;
	
	/**
	 * 响应字符串
	 */
	private String result="";
	
	
	/**
	 * 是否报错  fals 报错，true 正常
	 */
	private  boolean  isSuccess;
	
	/**
	 * 错误概要
	 */
	private String errMsg="";
	
	
	
}
