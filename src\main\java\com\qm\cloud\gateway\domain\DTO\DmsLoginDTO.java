package com.qm.cloud.gateway.domain.DTO;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.AllArgsConstructor;
import lombok.Data;


@Schema(description = "数据DMS 登录 DTO")
@Data
@AllArgsConstructor
public class DmsLoginDTO {

    @Schema(description = "账号")

    private String username;

    @Schema(description = "人员ID")
    private String personid;

    @Schema(description = "签名")
    private String vsign;

    @Schema(description = "系统名称，默认DMS")
    private String sysname;
}
