@echo off
REM 声明采用UTF-8编码
chcp 65001
echo bat文件将服务更新到本地base-start
echo http://jfapp.qm.cn/confluence/pages/viewpage.action?pageId=17204653
set base_start_dir=C:\Users\<USER>\idea_project\qm_server\base-start
set cur_version=1.0.0
setlocal EnableDelayedExpansion
set last_sentence=null
for /f "tokens=1-3 delims=:" %%a in (%~dp0src/main/resources/bootstrap.yml) do (
    if "!last_sentence: =!" equ "profile" (
    set key_word=%%a
    set key_value=%%b
        if "!key_word: =!" equ "enabled" (
        if "!key_value: =!" equ "false" (
            echo 现在处于本地配置文件生效状态，请修改bootstrap.yml中的配置！
            exit
        )
        )
    )
    set last_sentence=%%a
)
set nodes=name
set server_name="this is temp value"
for /F "tokens=2-3 delims=<>" %%a in (%~dp0pom.xml) do (
    set "node=%%a"
    if "!node:~0,5!" equ "%nodes%" (
        set target_value=%%b
        if "!target_value: =!" equ "!target_value!" (
        if "!target_value:-=!" neq "!target_value!" (
            set server_name=!target_value!
        )
        )
    )
)
if !server_name! equ "this is temp value" (
    echo 未读取到server_name
    exit
)
echo 服务名为：!server_name!
echo 版本号为：%cur_version%
echo 本地start-base所在目录为%base_start_dir%
echo 被打包的文件为：%~dp0target\!server_name!-%cur_version%.jar
set /p startip=是否继续，继续请输入Y，退出输入任意键
if %startip% neq Y ( exit )
echo 开始clean
call mvn clean -f %~dp0pom.xml
echo 开始install
call mvn install -DskipTests=true -f %~dp0pom.xml
copy %~dp0target\!server_name!-%cur_version%.jar %base_start_dir%\!server_name!.jar
exit
