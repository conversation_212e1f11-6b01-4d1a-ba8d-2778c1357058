package com.qm.cloud.gateway.jwt;

import com.qm.tds.api.exception.QmException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA加密工具
 *
 * <AUTHOR>
 */
@Slf4j
public class RsaKeyHelper {
    /**
     * 获取公钥
     *
     * @param filename
     * @return
     * @throws Exception
     */
    public PublicKey getPublicKey(String filename) throws QmException {
        InputStream resourceAsStream = getClass().getClassLoader().getResourceAsStream(filename);
        assert resourceAsStream != null;
        byte[] bytes = new byte[0];
        try {
            bytes = IOUtils.toByteArray(resourceAsStream);
            byte[] keyBytes = Base64.decodeBase64(bytes);
            X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
            KeyFactory kf = null;
            kf = KeyFactory.getInstance("RSA");
            return kf.generatePublic(spec);
        } catch (IOException e) {
            throw new QmException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new QmException(e);
        } catch (InvalidKeySpecException e) {
            throw new QmException(e);
        }
    }

    /**
     * 获取密钥
     *
     * @param filename
     * @return
     * @throws Exception
     */
    public PrivateKey getPrivateKey(String filename) throws IOException, NoSuchAlgorithmException, InvalidKeySpecException {

        InputStream resourceAsStream = getClass().getClassLoader().getResourceAsStream(filename);
        assert resourceAsStream != null;
        byte[] bytes = IOUtils.toByteArray(resourceAsStream);
        byte[] keyBytes = Base64.decodeBase64(bytes);

        PKCS8EncodedKeySpec spec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(spec);
    }

    /**
     * 生存rsa公钥和密钥
     *
     * @param publicKeyFilename
     * @param privateKeyFilename
     * @param password
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    public void generateKey(String publicKeyFilename, String privateKeyFilename, String password) throws NoSuchAlgorithmException {
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器，密钥大小为96-1024位
        keyPairGen.initialize(2048, new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();   // 得到私钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();  // 得到公钥
        try (FileOutputStream fos = new FileOutputStream(publicKeyFilename)){
            byte[] publicKeyBytes = Base64.encodeBase64(publicKey.getEncoded());
            fos.write(publicKeyBytes);
        } catch (Exception e) {
            log.info("---error--"+e.getMessage(), e);
        }
        try (FileOutputStream fos = new FileOutputStream(privateKeyFilename)){
            byte[] privateKeyBytes = Base64.encodeBase64(privateKey.getEncoded());
            fos.write(privateKeyBytes);
        }catch (Exception e){
            log.info("---error--"+e.getMessage(), e);
        }
    }

}

