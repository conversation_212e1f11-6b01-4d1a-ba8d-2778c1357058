package com.qm.cloud.gateway.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * 登录成功后返回的用户信息。
 * 下面的信息还是有些多，需要再删除一些无用字段。
 */
@Schema(title = "登录信息", description = "登录信息")
@Data
public class LoginVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    @Schema(title = "人员代码:人员代码", description = "人员代码:人员代码")
    private String vpersoncode;

    @Schema(title = "本公司标识:标识人员是否属于本公司，如果属于则对应部门，如不属于则为合作伙伴", description = "本公司标识:标识人员是否属于本公司，如果属于则对应部门，如不属于则为合作伙伴")
    private String vlocalcompanyflag;

    @Schema(title = "部门ID:人员所属部门", description = "部门ID:人员所属部门")
    private String ndeptid;

    @Schema(title = "登录标识:标识此人是否有登录系统权限", description = "登录标识:标识此人是否有登录系统权限")
    private String vlogingflag;

    @Schema(title = "登录用户名:系统登录用户名，全局唯一", description = "登录用户名:系统登录用户名，全局唯一")
    private String vusername;

    @Schema(title = "性别:性别 1男 2女", description = "性别:性别 1男 2女")
    private String vsex;

    @Schema(title = "参加工作时间:人员参加工作时间", description = "参加工作时间:人员参加工作时间")
    private Date dstarworkdate;

    @Schema(title = "岗位:人员所属岗位，适用于本公司人员", description = "岗位:人员所属岗位，适用于本公司人员")
    private String vstation;

    @Schema(title = "生日:人员的生日", description = "生日:人员的生日")
    private Date dbirthday;

    @Schema(title = "身份证号:人员的身份证号", description = "身份证号:人员的身份证号")
    private String vidcard;

    @Schema(title = "地址:客户所在地址", description = "地址:客户所在地址")
    private String vaddress;

    @Schema(title = "联系电话:客户的联系电话，固定电话", description = "联系电话:客户的联系电话，固定电话")
    private String vphone;

    @Schema(title = "移动电话:客户联系手机号码", description = "移动电话:客户联系手机号码")
    private String vmobile;

    @Schema(title = "传真:客户的传真号码", description = "传真:客户的传真号码")
    private String vfaxno;

    @Schema(title = "邮政编码:客户的邮政编码", description = "邮政编码:客户的邮政编码")
    private String vpostno;

    @Schema(title = "电子邮件:客户的电子邮件地址", description = "电子邮件:客户的电子邮件地址")
    private String vemail;

    @Schema(title = "网址:客户的网址", description = "网址:客户的网址")
    private String vurl;

    @Schema(title = "国家和地区:国家", description = "国家和地区:国家")
    private String vcountrycode;

    @Schema(title = "省区:省区", description = "省区:省区")
    private String nprovince;

    @Schema(title = "市县:市县", description = "市县:市县")
    private String ncity;

    @Schema(title = "备注:备注", description = "备注:备注")
    private String vremark;

    @Schema(title = "使用证书:标识此人登录系统是否使用证书，默认不使用", description = "使用证书:标识此人登录系统是否使用证书，默认不使用")
    private String vusecertificate;

    @Schema(title = "公司ID:公司ID", description = "公司ID:公司ID")
    private String ncompanyid;

    @Schema(title = "停用标识:停止使用标识", description = "停用标识:停止使用标识")
    private String vstop;

    @Schema(title = "时间戳: ", description = "时间戳: ")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Timestamp dtstamp;

    @Schema(title = "加入公司日期", description = "加入公司日期")
    private Date dhiredate;

    @Schema(title = "离职日期", description = "离职日期")
    private Date dleavedate;

    @Schema(title = "民族代码", description = "民族代码")
    private String vnationality;

    @Schema(title = "职务代码，关联数据字典DUTY", description = "职务代码，关联数据字典DUTY")
    private String vduty;

    @Schema(title = "职称代码", description = "职称代码")
    private String vtechpost;

    @Schema(title = "职工编号", description = "职工编号")
    private String vworkerno;

    @Schema(title = "文化程度代码", description = "文化程度代码")
    private String vdegree;

    @Schema(title = "职员类别代码", description = "职员类别代码")
    private String vempgroup;

    @Schema(title = "县", description = "县")
    private String ncountry;

    @Schema(title = "用户选择的语言代码", description = "用户选择的语言代码")
    private String vcurrentlanguagecode;

    @Schema(title = "客户组ID", description = "客户组ID")
    private String ncustgroupid;

    @Schema(title = "手机串号绑定标识", description = "手机串号绑定标识")
    private String vimeiident;

    @Schema(title = "限制登录标识", description = "限制登录标识")
    private String vloginlimit;

    @Schema(title = "锁定日期", description = "锁定日期")
    private Date dloginlimit;

    @Schema(title = "锁定原因", description = "锁定原因")
    private String vlimitreason;

    @Schema(title = "真实姓名", description = "真实姓名")
    private String vrealname;

    @Schema(title = "部门名称", description = "部门名称")
    private String deptname;

    @Schema(title = "登录时间戳", description = "登录时间戳")
    private String loginTimestamp;

    @SuppressWarnings("squid:S1948")
    @Schema(title = "人员对应组织", description = "人员对应组织")
    private List personOrganizeDOList;
}
