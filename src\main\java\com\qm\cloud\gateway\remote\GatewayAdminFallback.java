package com.qm.cloud.gateway.remote;

import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class GatewayAdminFallback implements FallbackFactory<GatewayAdminRemote> {
    @Override
    public GatewayAdminRemote create(Throwable throwable) {
        GatewayAdminHystrix remote = new GatewayAdminHystrix();
        remote.setHystrixEx(throwable);
        return remote;
    }
}
