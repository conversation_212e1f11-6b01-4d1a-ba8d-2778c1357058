package com.qm.cloud.gateway;

import com.qm.tds.api.util.SpringContextHolder;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScans;
import org.springframework.context.annotation.FilterType;


@SpringBootApplication
@EnableDiscoveryClient
@EnableHystrix
@EnableFeignClients
@ComponentScans(value = {
        @ComponentScan(basePackages = {"com.qm.cloud", "com.qm.tds.tsf"}),
        @ComponentScan(basePackages = "com.qm.tds.util",
                includeFilters = {@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE,
                        classes = {com.qm.tds.util.I18nUtil.class,com.qm.tds.util.RedisUtils.class})},
                useDefaultFilters = false),//扫描组件
        @ComponentScan(basePackages = "com.qm.tds.api.util", useDefaultFilters = false,
                includeFilters = {@ComponentScan.Filter(value = SpringContextHolder.class, type = FilterType.ASSIGNABLE_TYPE)})})
public class CloudGatewayServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(CloudGatewayServerApplication.class, args);
    }
}
