package com.qm.cloud.gateway.jwt;

/**
 * String Util
 *
 * <AUTHOR>
 */
public class StringHelper {
    /**
     * 这个类不能实例化
     */
    private StringHelper() {
    }

    public static String getObjectValue(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    public static boolean isNullOrEmpty(String str) {
        if (str == null || "".equals(str)) {
            return true;
        } else {
            return false;
        }
    }
}
