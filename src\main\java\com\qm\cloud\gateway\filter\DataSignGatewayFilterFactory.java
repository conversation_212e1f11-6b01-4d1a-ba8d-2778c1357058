package com.qm.cloud.gateway.filter;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.qm.cloud.gateway.util.LogStashUtils;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.MD5Utils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * 数据签名过滤器
 *
 * <AUTHOR>
 * @date 2021/4/9 17:22
 */
@Slf4j
@Component
public class DataSignGatewayFilterFactory extends AbstractGatewayFilterFactory<DataSignGatewayFilterFactory.Config> {
    @Autowired
    private I18nUtil i18nUtil;
    private static final String HOUR_DATE_FORMAT = "yyyyMMddHH";

    private Integer httpRequestFilterOrder;

    public DataSignGatewayFilterFactory() {
        super(Config.class);
    }

    /**
     * 数据签名过滤器
     * 此过滤器必须在HttpRequestFilter后执行
     * 有关此过滤器获取请求体的方法，请参考{@link HttpRequestFilter#handleRequestBody(ServerWebExchange, String)}
     *
     * @param config 路由配置
     * @return GatewayFilter
     */
    @SuppressWarnings("squid:S3776")
    @Override
    public GatewayFilter apply(DataSignGatewayFilterFactory.Config config) {
        if (httpRequestFilterOrder == null) {
            httpRequestFilterOrder = new HttpRequestFilter().getOrder();
        }
        if (config.getOrder() < httpRequestFilterOrder) {
            String message = i18nUtil.getMessage("ERR.gatewayservice.dataSignGatewayFilter.fail");
            return (exchange, chain) -> authorizeFailure(exchange, message);
        }
        return new OrderedGatewayFilter((exchange, chain) -> {
            ServerHttpRequest request = exchange.getRequest();
            HttpMethod method = request.getMethod();
            if (HttpMethod.POST.equals(method)) {
                // 获取HttpRequestFilter缓存的requestBody
                String reqBody = (String) exchange.getAttributes().get(LogStashUtils.CACHE_REQUEST_BODY_OBJECT_KEY);
                // 文件上传和请求体为空时，不需验签
                if (HttpRequestFilter.FILE_UPLOAD_REQUEST_BODY.equals(reqBody) || StringUtils.isBlank(reqBody)) {
                    return chain.filter(exchange.mutate().request(request).build());
                }
                MediaType contentType = request.getHeaders().getContentType();
                // 根据HttpRequestFilter缓存请求体的规则反解析，获取原始请求体
                if (contentType == null || !contentType.includes(MediaType.APPLICATION_JSON)) {
                    if (reqBody.startsWith("{") && reqBody.endsWith("}")) {
                        reqBody = reqBody.substring(1, reqBody.length() - 1);
                    }
                }
                reqBody = JSON.parse(reqBody).toString();
                log.debug("[-DataSignGatewayFilterFactory-].apply:reqBody={}", reqBody);
                String apisign = request.getHeaders().getFirst("apisign");
                String path = request.getURI().getPath();
                log.debug("[-DataSignGatewayFilterFactory-].apply:path={}", path);
                String oneHourBefore = DateUtil.offsetHour(new Date(), -1).toString(HOUR_DATE_FORMAT);
                String now = DateUtil.format(new Date(), HOUR_DATE_FORMAT);
                String oneHourAfter = DateUtil.offsetHour(new Date(), 1).toString(HOUR_DATE_FORMAT);
                log.debug("[-DataSignGatewayFilterFactory-].apply:oneHourBefore={}", oneHourBefore);
                log.debug("[-DataSignGatewayFilterFactory-].apply:now={}", now);
                log.debug("[-DataSignGatewayFilterFactory-].apply:oneHourAfter={}", oneHourAfter);
                String digestNow = MD5Utils.encrypt(path + "_" + now + "_" + reqBody);
                String digestBefore = MD5Utils.encrypt(path + "_" + oneHourBefore + "_" + reqBody);
                String digestAfter = MD5Utils.encrypt(path + "_" + oneHourAfter + "_" + reqBody);
                if (digestNow.equals(apisign) || digestBefore.equals(apisign) || digestAfter.equals(apisign)) {
                    return chain.filter(exchange.mutate().request(request).build());
                } else {
                    String message = i18nUtil.getMessage("ERR.gatewayservice.dataSignGatewayFilter.signError");
                    return authorizeFailure(exchange, message);
                }
            }
            return chain.filter(exchange.mutate().request(request).build());
        }, config.getOrder());
    }

    /**
     * 验签失败，返回错误信息
     *
     * @param exchange HTTP交换机
     * @return 包含错误信息的Mono
     */
    private Mono<Void> authorizeFailure(ServerWebExchange exchange, String msgErr) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.OK);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        JsonResultVo res = new JsonResultVo();
        res.setMsgErr(msgErr);
        res.setCode(HttpStatus.INTERNAL_SERVER_ERROR.value());
        String body = JSON.toJSONString(res);
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 对应 配置管理-网关配置-过滤器配置-参数
     * DataSign=[order]
     * 例如：DataSign=-5
     */
    @Data
    public static class Config {
        // 排序
        private int order;
    }
}
