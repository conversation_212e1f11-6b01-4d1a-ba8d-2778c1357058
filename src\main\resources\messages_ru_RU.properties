#这里填写俄文翻译
MSG.gatewayservice.common.reflushSuccess=Обновление удалось!
MSG.gatewayservice.getAllFilter.success=Получение фильтров шлюза всех систем удалось.
ERR.gatewayservice.dataSignGatewayFilter.fail=Связаться с администратором, чтобы повторно конфигурировать фильтр шлюза DataSign. Приоритет DataSign не может иметь приоритет над (меньше, чем) глобальным фильтром HttpRequestFilter.
ERR.gatewayservice.dataSignGatewayFilter.signError=Неправильная подпись данных
ERR.gatewayservice.common.noauth=Нет разрешения на доступ
ERR.gatewayservice.epAuthorizeGatewayFilter.useragent=Useragent в header HTTP-запроса не может быть пустым!
ERR.gatewayservice.httpResponse.toobig=Информация о возвращаемом контенте слишком велика
ERR.gatewayservice.exHandler.responseStatus=Запрошенный ресурс %s не существует, связаться с системным администратором, чтобы проверить правильность URI, и сконфигурированы ли соответствующие утверждения и маршруты!
ERR.gatewayservice.exHandler.resourceNotFound=Ресурс не найден
ERR.gatewayservice.exHandler.nullPointer=Исключение в пустом указателе
ERR.gatewayservice.exHandler.timeout=Тайм-аут запроса
ERR.gatewayservice.exHandler.hystrixRuntime=Загруженный вами прикрепленный файл, может быть, превысил предельный размер прикрепленного файла системы %s!
ERR.gatewayservice.exHandler.isNullOrEmpty=Исключение в запросе
ERR.gatewayservice.remote.getHystrixEx=Исключение при вызове интерфейса
ERR.gatewayservice.gatewayService.refresh=Проверить, запущена ли служба gateway-adminа
ERR.gatewayservice.authService.jwtToken=Данные для аутентификации неверны, повторно войти в систему
ERR.gatewayservice.authService.appkey=Appkey в Header не должен быть пустым!
ERR.gatewayservice.authService.overtime=Тайм-аут входа в систему, повторно войти в систему
ERR.gatewayservice.common.generateJwtFail=Не удалось сгенерировать jwt
ERR.gatewayservice.common.sendCodeAgain=Отправить код подтверждения повторно
ERR.gatewayservice.common.codeWrong=Ошибочный проверочный код
ERR.gatewayservice.common.paramWrong=Ошибочный параметр
