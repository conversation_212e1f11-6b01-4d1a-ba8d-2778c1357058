package com.qm.cloud.gateway.filter;

import lombok.Data;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.NettyWriteResponseFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.factory.rewrite.ModifyResponseBodyGatewayFilterFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

//@Slf4j
@Component
public class RecordResponseBodyGatewayFilterFactory extends AbstractGatewayFilterFactory<RecordResponseBodyGatewayFilterFactory.Config>{
	private static final String RESPONSE_BODY_OBJECT_KEY = "responseBodyObject";
    private ModifyResponseBodyGatewayFilterFactory modifyResponseBodyGatewayFilterFactory;

	public RecordResponseBodyGatewayFilterFactory(ModifyResponseBodyGatewayFilterFactory modifyResponseBodyGatewayFilterFactory) {
		super(Config.class);
    	this.modifyResponseBodyGatewayFilterFactory = modifyResponseBodyGatewayFilterFactory;
	}

	@Override
	public GatewayFilter apply(Config config) {
		return new OrderedGatewayFilter((exchange, chain) -> {
			GatewayFilter gatewayFilter = modifyResponseBodyGatewayFilterFactory
					.apply(cfg -> cfg.setRewriteFunction(String.class, String.class, (exchg, s) -> {
						exchange.getAttributes().put(RESPONSE_BODY_OBJECT_KEY, s);
						return Mono.just(s);
					}));
			return gatewayFilter.filter(exchange, chain);
		}, NettyWriteResponseFilter.WRITE_RESPONSE_FILTER_ORDER - 1);
	}

	@Data
	public static class Config {
	}
}
