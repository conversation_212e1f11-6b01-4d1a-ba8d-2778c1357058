package com.qm.cloud.gateway.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.qm.cloud.gateway.constant.GatewayConstants;
import com.qm.cloud.gateway.domain.DTO.OnlineUserDTO;
import com.qm.cloud.gateway.jwt.IJwtInfo;
import com.qm.cloud.gateway.service.AuthService;
import com.qm.cloud.gateway.service.AuthUtilService;
import com.qm.cloud.gateway.util.JwtTokenUtil;
import com.qm.tds.api.domain.JsonResultVo;
import com.qm.tds.api.exception.QmException;
import com.qm.tds.util.BootAppUtil;
import com.qm.tds.util.I18nUtil;
import com.qm.tds.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2020/12/12$ 14:37$
 **/
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private JwtTokenUtil jwtTokenUtil;
    @Autowired
    private AuthUtilService authUtilService;
    @Autowired
    private I18nUtil i18nUtil;
    // 返回的jwt是否互踢，默认互踢。
    @Value("${jwt.expire.kickAble:true}")
    public boolean kickAble;
    // 是否按照设备id互踢，默认否
    @Value("${jwt.expire.kickByClientId:true}")
    public boolean kickByClientId;

    @SuppressWarnings("squid:S3776")
    @Override
    public JsonResultVo<IJwtInfo> getUserInfoByJwt(String jwtToken, String userType, String urlPath, String appKey) throws QmException {
        JsonResultVo<IJwtInfo> result = new JsonResultVo<>();
        result.setCode(200);
        try {
            // 根据jwt取jwtInfo
            String jwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, userType, jwtToken);
            Object jwtInfo = redisUtils.get(jwtKey);
            if (!BootAppUtil.isNullOrEmpty(jwtInfo)) {
                // 取得登录账号
                String loginName = ((IJwtInfo) jwtInfo).getUniqueName();
                String loginNameKey = redisUtils.keyBuilder(GatewayConstants.LOGINNAME, userType, loginName);
                long expireTime = jwtTokenUtil.getExpire(userType, GatewayConstants.BASE);
                // 根据账号取得有效的jwt
                Object jwt = redisUtils.get(loginNameKey);
                if (!BootAppUtil.isNullOrEmpty(jwt)) {
                    // 当前jwt与有效jwt是否匹配
                    boolean r = jwtToken.equals(jwt);
                    log.info("--jwt,getUserInfoByJwt r:{}", r);
                    // 当前jwt与有效jwt匹配
                    if (r) {
                        // 设置jwt有效期
                        redisUtils.expire(jwtKey, expireTime);
                        // 设置loginName有效期
                        redisUtils.expire(loginNameKey, expireTime, TimeUnit.SECONDS);
                        result.setData((IJwtInfo) jwtInfo);
                    } else {
                        //删除失效的jwt
                        redisUtils.del(jwtKey);
                        String message = i18nUtil.getMessage("ERR.gatewayservice.authService.jwtToken");
                        result.setMsgErr(message);
                    }
                } else {// 根据账号没有找到有效的jwt，则认为当前这个jwt即为有效的jwt
                    log.info("--根据登录账号没有找到有效的jwt");
                    // 缓存登录账号有效的jwt
                    redisUtils.set(loginNameKey, jwtToken, expireTime);
                    // 设置jwt有效期
                    redisUtils.expire(jwtKey, expireTime);
                    result.setData((IJwtInfo) jwtInfo);
                }
                //判断是否有接口访问权限
                log.info("---error--"+"--------------------------请求urlPath：{}", urlPath);
                if (null != ((IJwtInfo) jwtInfo).getParam().get("nitegrationAPI")) {
                    if (!BootAppUtil.isNullOrEmpty(appKey)) {
                        Boolean reFlag = false;
                        JSONArray apis = (JSONArray) JSONObject.parse(((IJwtInfo) jwtInfo).getParam().get("nitegrationAPI").toString());
                        for (int i = 0; i < apis.size(); i++) {
                            String vapiregular = ((JSONObject) apis.get(i)).get("vapiregular").toString();
                            if (urlPath.matches(vapiregular)) {
                                reFlag = true;
                                break;
                            }
                        }
                        if (!reFlag) {
                            String message = i18nUtil.getMessage("ERR.gatewayservice.common.noauth");
                            result.setMsgErr(message);
                        }
                    } else {
                        String message = i18nUtil.getMessage("ERR.gatewayservice.authService.appkey");
                        result.setMsgErr(message);
                    }
                }
            } else {
                String message = i18nUtil.getMessage("ERR.gatewayservice.authService.overtime");
                result.setMsgErr(message);
            }
        } catch (Exception e) {
            log.info("--redis发生异常，从jwt取信息");
            IJwtInfo info = authUtilService.getInfoFromToken(jwtToken);
            // 根据jwt缓存用户信息 数据存储key
            String jwtKey = redisUtils.keyBuilder(GatewayConstants.JWT, userType, jwtToken);
            long expireTime = jwtTokenUtil.getExpire(userType, GatewayConstants.BASE);
            redisUtils.set(jwtKey, info, expireTime);
            result.setData(info);
        }
        return result;
    }

    @Override
    public Long offline(List<OnlineUserDTO> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return 0L;
        }

        // 非空与非法字符校验
        List<String> uLoginNameKeys = userIds.parallelStream().filter(Objects::nonNull)
                .map(o -> {
                    String clientId;
                    if (StringUtils.isEmpty(o.getTerminal()))
                        clientId = "pc";
                    else
                        clientId = o.getTerminal().toLowerCase();
                    Boolean fixedKickByClientId = kickAble && kickByClientId;
                    String userName = fixedKickByClientId && clientId != null ? o.getUserId() + GatewayConstants.JWT_CLIENT_DELIMITER + clientId : o.getUserId();
                    String uLoginNameKey = "uLoginName:[" + clientId + "]:" + userName;
                    String curJwt = (String) redisUtils.get(uLoginNameKey);
                    Boolean deleted = redisUtils.del("jwt:[" + clientId + "]:" + curJwt);
                    if (deleted == null || !deleted) {
                        log.info("---error--"+"[-OnlineUserServiceImpl-].offline:uLoginNameKey={}", uLoginNameKey);
                    }
                    return uLoginNameKey;
                })
                .collect(Collectors.toList());
        return redisUtils.del(uLoginNameKeys);
    }
}
