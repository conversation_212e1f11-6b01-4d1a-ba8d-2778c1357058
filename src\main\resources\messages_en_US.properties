MSG.gatewayservice.common.reflushSuccess=Refresh succeeded!
MSG.gatewayservice.getAllFilter.success=All system gateway filters acquisition succeeded
ERR.gatewayservice.dataSignGatewayFilter.fail=Please contact the administrator to reconfigure the DataSign gateway filter, the DataSign priority cannot be prior to (less than) the HttpRequestFilter global filter
ERR.gatewayservice.dataSignGatewayFilter.signError=Incorrect data signature
ERR.gatewayservice.common.noauth=No access
ERR.gatewayservice.epAuthorizeGatewayFilter.useragent=The useragent in the HTTP request header cannot be empty!
ERR.gatewayservice.httpResponse.toobig=Return content information is too large
ERR.gatewayservice.exHandler.responseStatus=The requested resource %s does not exist, please contact the system administrator to check whether the URI is correct and whether the corresponding assertion and routing are configured!
ERR.gatewayservice.exHandler.resourceNotFound=Resource not found
ERR.gatewayservice.exHandler.nullPointer=Abnormal null pointer
ERR.gatewayservice.exHandler.timeout=Request timed out
ERR.gatewayservice.exHandler.hystrixRuntime=The attachment you uploaded may exceed the attachment size limit of the %s system!
ERR.gatewayservice.exHandler.isNullOrEmpty=Abnormal request:
ERR.gatewayservice.remote.getHystrixEx=The calling interface is abnormal!
ERR.gatewayservice.gatewayService.refresh=Please check whether the gateway-admin service is started
ERR.gatewayservice.authService.jwtToken=The authentication information is incorrect, please log in again
ERR.gatewayservice.authService.appkey=The appkey in the Header should not be null!
ERR.gatewayservice.authService.overtime=Login timed out, please login again
ERR.gatewayservice.common.generateJwtFail=jwt generation failed
ERR.gatewayservice.common.sendCodeAgain=Please resend the verification code
ERR.gatewayservice.common.codeWrong=Verification code error
ERR.gatewayservice.common.paramWrong=Parameter error
